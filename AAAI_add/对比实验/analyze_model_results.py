#!/usr/bin/env python3
"""
Script to analyze and compare experimental results from TimeMixer, CycleNet, and SOFTS models.
Processes result files and creates comprehensive comparison tables.
"""

import pandas as pd
import re
import os
import math
from pathlib import Path

def truncate_to_3_decimals(value):
    """
    Truncate a number to exactly 3 decimal places (no rounding).

    Args:
        value (float): Number to truncate

    Returns:
        float: Truncated value to 3 decimal places
    """
    if pd.isna(value):
        return value
    # Multiply by 1000, truncate to integer, then divide by 1000
    return math.floor(value * 1000) / 1000

def parse_result_file(file_path, model_name):
    """
    Parse a result file and extract performance metrics.
    
    Args:
        file_path (str): Path to the result file
        model_name (str): Name of the model (TimeMixer, CycleNet, SOFTS)
    
    Returns:
        list: List of dictionaries containing parsed results
    """
    results = []
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # Skip empty lines
            if not line:
                i += 1
                continue
            
            # Look for experiment identifier lines
            if any(keyword in line for keyword in ['_sl', '_pl', '_96_', '_192_', '_336_', '_720_']):
                experiment_id = line
                
                # Look for the next line with MSE and MAE
                if i + 1 < len(lines):
                    metrics_line = lines[i + 1].strip()
                    
                    # Parse MSE and MAE using regex
                    mse_match = re.search(r'mse:([\d.]+)', metrics_line)
                    mae_match = re.search(r'mae:([\d.]+)', metrics_line)
                    
                    if mse_match and mae_match:
                        mse = float(mse_match.group(1))
                        mae = float(mae_match.group(1))
                        
                        # Extract dataset and prediction length from experiment_id
                        dataset, pred_len = extract_dataset_and_pred_len(experiment_id, model_name)
                        
                        if dataset and pred_len:
                            results.append({
                                'model': model_name,
                                'dataset': dataset,
                                'pred_len': pred_len,
                                'dataset_pred': f"{dataset}_{pred_len}",
                                'mse': mse,
                                'mae': mae,
                                'experiment_id': experiment_id
                            })
                
                i += 2  # Skip the metrics line
            else:
                i += 1
    
    except Exception as e:
        print(f"Error parsing {file_path}: {e}")
    
    return results

def extract_dataset_and_pred_len(experiment_id, model_name):
    """
    Extract dataset name and prediction length from experiment identifier.
    
    Args:
        experiment_id (str): Experiment identifier string
        model_name (str): Model name for context
    
    Returns:
        tuple: (dataset_name, prediction_length)
    """
    try:
        # Different patterns for different models
        if model_name == 'TimeMixer':
            # Pattern: TimeMixer_dataset_sl96_pl192
            match = re.search(r'TimeMixer_([^_]+)_sl\d+_pl(\d+)', experiment_id)
            if match:
                return match.group(1), int(match.group(2))
        
        elif model_name == 'CycleNet':
            # Pattern: dataset_mlp_96_192
            match = re.search(r'([^_]+)_mlp_\d+_(\d+)', experiment_id)
            if match:
                return match.group(1), int(match.group(2))
        
        elif model_name == 'SOFTS':
            # Pattern: SOFTS_dataset_sl96_pl192
            match = re.search(r'SOFTS_([^_]+)_sl\d+_pl(\d+)', experiment_id)
            if match:
                return match.group(1), int(match.group(2))
    
    except Exception as e:
        print(f"Error extracting dataset and pred_len from {experiment_id}: {e}")
    
    return None, None

def create_comparison_tables(all_results):
    """
    Create comparison tables from all results.

    Args:
        all_results (list): List of all parsed results

    Returns:
        tuple: (wide_format_df, summary_df)
    """
    # Convert to DataFrame
    df = pd.DataFrame(all_results)

    if df.empty:
        print("No results found!")
        return None, None

    # Create pivot table for MSE
    mse_pivot = df.pivot_table(
        index='dataset_pred',
        columns='model',
        values='mse',
        aggfunc='first'
    )

    # Create pivot table for MAE
    mae_pivot = df.pivot_table(
        index='dataset_pred',
        columns='model',
        values='mae',
        aggfunc='first'
    )

    # Combine MSE and MAE into a single table
    comparison_df = pd.DataFrame()

    for model in ['TimeMixer', 'CycleNet', 'SOFTS']:
        if model in mse_pivot.columns:
            comparison_df[f'{model}_MSE'] = mse_pivot[model]
        if model in mae_pivot.columns:
            comparison_df[f'{model}_MAE'] = mae_pivot[model]

    # Custom sorting: by dataset name first, then by prediction length
    def sort_key(index_name):
        parts = index_name.split('_')
        if len(parts) >= 2:
            dataset = '_'.join(parts[:-1])  # Everything except the last part
            try:
                pred_len = int(parts[-1])  # Last part should be prediction length
                return (dataset, pred_len)
            except ValueError:
                return (dataset, 999)  # If can't parse pred_len, put at end
        return (index_name, 999)

    # Sort the index using custom key
    sorted_indices = sorted(comparison_df.index, key=sort_key)
    comparison_df = comparison_df.reindex(sorted_indices)

    # Create summary statistics
    summary_stats = []
    for model in ['TimeMixer', 'CycleNet', 'SOFTS']:
        model_data = df[df['model'] == model]
        if not model_data.empty:
            summary_stats.append({
                'Model': model,
                'Avg_MSE': model_data['mse'].mean(),
                'Avg_MAE': model_data['mae'].mean(),
                'Min_MSE': model_data['mse'].min(),
                'Min_MAE': model_data['mae'].min(),
                'Max_MSE': model_data['mse'].max(),
                'Max_MAE': model_data['mae'].max(),
                'Num_Experiments': len(model_data)
            })

    summary_df = pd.DataFrame(summary_stats)

    return comparison_df, summary_df

def create_beautiful_table(df):
    """
    Create an extremely beautiful and professional table with enhanced visual appeal.

    Args:
        df (DataFrame): DataFrame to format

    Returns:
        str: Formatted table string
    """
    if df is None or df.empty:
        return "No data available"

    # Apply truncation to all numeric values
    df_display = df.copy()
    for col in df_display.columns:
        df_display[col] = df_display[col].apply(truncate_to_3_decimals)

    # Prepare data structure for new table format
    table_data = []

    # Sort indices by dataset and prediction length
    sorted_indices = sorted(df_display.index, key=lambda x: (
        '_'.join(x.split('_')[:-1]),  # dataset name
        int(x.split('_')[-1])         # prediction length
    ))

    for idx in sorted_indices:
        parts = idx.split('_')
        dataset = '_'.join(parts[:-1])
        pred_len = parts[-1]

        row_data = {
            'Dataset': dataset,
            'Pred_Len': pred_len,
            'TimeMixer_MSE': df_display.loc[idx, 'TimeMixer_MSE'] if 'TimeMixer_MSE' in df_display.columns else None,
            'TimeMixer_MAE': df_display.loc[idx, 'TimeMixer_MAE'] if 'TimeMixer_MAE' in df_display.columns else None,
            'CycleNet_MSE': df_display.loc[idx, 'CycleNet_MSE'] if 'CycleNet_MSE' in df_display.columns else None,
            'CycleNet_MAE': df_display.loc[idx, 'CycleNet_MAE'] if 'CycleNet_MAE' in df_display.columns else None,
            'SOFTS_MSE': df_display.loc[idx, 'SOFTS_MSE'] if 'SOFTS_MSE' in df_display.columns else None,
            'SOFTS_MAE': df_display.loc[idx, 'SOFTS_MAE'] if 'SOFTS_MAE' in df_display.columns else None
        }
        table_data.append(row_data)

    # Enhanced column widths for better visual appeal
    col_widths = [14, 10, 10, 10, 10, 10, 10, 10]

    # Create table with enhanced visual elements
    table_lines = []

    # Decorative top border
    table_lines.append("╔" + "╦".join("═" * width for width in col_widths) + "╗")

    # Main header row with enhanced styling
    header_row = "║"
    headers = ['Dataset', 'Pred_Len', 'TimeMixer', '', 'CycleNet', '', 'SOFTS', '']
    for i, (header, width) in enumerate(zip(headers, col_widths)):
        if i in [2, 4, 6]:  # Model name columns - make them bold-looking
            header_row += f" {header:^{width-1}}║"
        elif i == 0:  # Dataset column
            header_row += f" {header:^{width-1}}║"
        elif i == 1:  # Pred_Len column
            header_row += f" {header:^{width-1}}║"
        else:
            header_row += f" {header:^{width-1}}║"
    table_lines.append(header_row)

    # Sub-header row with metric names
    sub_header_row = "║"
    sub_headers = ['', '', 'MSE', 'MAE', 'MSE', 'MAE', 'MSE', 'MAE']
    for i, (sub_header, width) in enumerate(zip(sub_headers, col_widths)):
        if i in [2, 3, 4, 5, 6, 7]:  # Metric columns
            sub_header_row += f" {sub_header:^{width-1}}║"
        else:
            sub_header_row += f" {sub_header:^{width-1}}║"
    table_lines.append(sub_header_row)

    # Enhanced header separator
    table_lines.append("╠" + "╬".join("═" * width for width in col_widths) + "╣")

    # Data rows with enhanced dataset grouping
    current_dataset = None
    for i, row_data in enumerate(table_data):
        dataset = row_data['Dataset']

        # Add enhanced separator between different datasets
        if current_dataset is not None and current_dataset != dataset:
            table_lines.append("╠" + "╬".join("═" * width for width in col_widths) + "╣")

        current_dataset = dataset

        # Data row with enhanced formatting
        row = "║"
        row += f" {dataset:<{col_widths[0]-1}}║"
        row += f" {row_data['Pred_Len']:^{col_widths[1]-1}}║"

        # Add metric values with enhanced formatting
        metrics = ['TimeMixer_MSE', 'TimeMixer_MAE', 'CycleNet_MSE', 'CycleNet_MAE', 'SOFTS_MSE', 'SOFTS_MAE']
        for j, metric in enumerate(metrics):
            val = row_data[metric]
            if pd.notna(val):
                formatted_val = f"{val:.3f}"
                # Highlight best values (lowest for both MSE and MAE)
                if j % 2 == 0:  # MSE columns
                    mse_values = [row_data[m] for m in ['TimeMixer_MSE', 'CycleNet_MSE', 'SOFTS_MSE'] if pd.notna(row_data[m])]
                    if mse_values and val == min(mse_values):
                        formatted_val = f"{val:.3f}*"  # Mark best MSE with asterisk
                else:  # MAE columns
                    mae_values = [row_data[m] for m in ['TimeMixer_MAE', 'CycleNet_MAE', 'SOFTS_MAE'] if pd.notna(row_data[m])]
                    if mae_values and val == min(mae_values):
                        formatted_val = f"{val:.3f}*"  # Mark best MAE with asterisk
            else:
                formatted_val = "N/A"
            row += f" {formatted_val:>{col_widths[j+2]-1}}║"

        table_lines.append(row)

    # Enhanced bottom border
    table_lines.append("╚" + "╩".join("═" * width for width in col_widths) + "╝")

    # Add legend for asterisks
    table_lines.append("")
    table_lines.append("Legend: * = Best performance (lowest value) for each dataset-prediction length combination")

    return "\n".join(table_lines)

def create_markdown_table(df):
    """
    Create a markdown formatted table with redesigned structure and truncated values.

    Args:
        df (DataFrame): DataFrame to format

    Returns:
        str: Markdown formatted table string
    """
    if df is None or df.empty:
        return "No data available"

    # Apply truncation to all numeric values
    df_display = df.copy()
    for col in df_display.columns:
        df_display[col] = df_display[col].apply(truncate_to_3_decimals)

    # Prepare data structure for new table format
    table_data = []

    # Sort indices by dataset and prediction length
    sorted_indices = sorted(df_display.index, key=lambda x: (
        '_'.join(x.split('_')[:-1]),  # dataset name
        int(x.split('_')[-1])         # prediction length
    ))

    for idx in sorted_indices:
        parts = idx.split('_')
        dataset = '_'.join(parts[:-1])
        pred_len = parts[-1]

        row_data = {
            'Dataset': dataset,
            'Pred_Len': pred_len,
            'TimeMixer_MSE': df_display.loc[idx, 'TimeMixer_MSE'] if 'TimeMixer_MSE' in df_display.columns else None,
            'TimeMixer_MAE': df_display.loc[idx, 'TimeMixer_MAE'] if 'TimeMixer_MAE' in df_display.columns else None,
            'CycleNet_MSE': df_display.loc[idx, 'CycleNet_MSE'] if 'CycleNet_MSE' in df_display.columns else None,
            'CycleNet_MAE': df_display.loc[idx, 'CycleNet_MAE'] if 'CycleNet_MAE' in df_display.columns else None,
            'SOFTS_MSE': df_display.loc[idx, 'SOFTS_MSE'] if 'SOFTS_MSE' in df_display.columns else None,
            'SOFTS_MAE': df_display.loc[idx, 'SOFTS_MAE'] if 'SOFTS_MAE' in df_display.columns else None
        }
        table_data.append(row_data)

    # Create markdown table with multi-level headers
    table_lines = []

    # Main header row
    header_row = "| Dataset | Pred_Len | TimeMixer |  | CycleNet |  | SOFTS |  |"
    table_lines.append(header_row)

    # Sub-header row
    sub_header_row = "| | | MSE | MAE | MSE | MAE | MSE | MAE |"
    table_lines.append(sub_header_row)

    # Separator row
    separator = "| --- | --- | --- | --- | --- | --- | --- | --- |"
    table_lines.append(separator)

    # Data rows with dataset grouping
    current_dataset = None
    for row_data in table_data:
        dataset = row_data['Dataset']

        # Add empty row between different datasets for better readability
        if current_dataset is not None and current_dataset != dataset:
            empty_row = "| | | | | | | | |"
            table_lines.append(empty_row)

        current_dataset = dataset

        # Format metric values with truncation
        metrics = ['TimeMixer_MSE', 'TimeMixer_MAE', 'CycleNet_MSE', 'CycleNet_MAE', 'SOFTS_MSE', 'SOFTS_MAE']
        formatted_metrics = []
        for metric in metrics:
            val = row_data[metric]
            if pd.notna(val):
                formatted_metrics.append(f"{val:.3f}")
            else:
                formatted_metrics.append("N/A")

        # Data row
        row = f"| {dataset} | {row_data['Pred_Len']} | {' | '.join(formatted_metrics)} |"
        table_lines.append(row)

    return "\n".join(table_lines)

def create_simple_markdown_table(df):
    """
    Create a simple markdown table from DataFrame with truncated values.

    Args:
        df (DataFrame): DataFrame to convert

    Returns:
        str: Markdown table string
    """
    if df is None or df.empty:
        return "No data available"

    # Create header
    headers = list(df.columns)
    header_row = "| " + " | ".join(headers) + " |"
    separator = "| " + " | ".join(["---"] * len(headers)) + " |"

    # Create data rows
    rows = [header_row, separator]
    for _, row in df.iterrows():
        row_data = []
        for col in headers:
            val = row[col]
            if pd.isna(val):
                row_data.append("N/A")
            elif isinstance(val, (int, float)):
                if isinstance(val, float):
                    # Apply truncation for float values
                    truncated_val = truncate_to_3_decimals(val)
                    row_data.append(f"{truncated_val:.3f}")
                else:
                    row_data.append(str(val))
            else:
                row_data.append(str(val))
        rows.append("| " + " | ".join(row_data) + " |")

    return "\n".join(rows)

def analyze_best_performers(comparison_df):
    """
    Analyze which model performs best for each dataset-prediction length combination.

    Args:
        comparison_df (DataFrame): Main comparison table

    Returns:
        tuple: (best_mse_df, best_mae_df, win_counts)
    """
    if comparison_df is None:
        return None, None, None

    # Extract MSE columns
    mse_cols = [col for col in comparison_df.columns if 'MSE' in col]
    mae_cols = [col for col in comparison_df.columns if 'MAE' in col]

    # Find best performers for MSE (lowest values)
    best_mse = comparison_df[mse_cols].idxmin(axis=1)
    best_mse_values = comparison_df[mse_cols].min(axis=1)

    # Find best performers for MAE (lowest values)
    best_mae = comparison_df[mae_cols].idxmin(axis=1)
    best_mae_values = comparison_df[mae_cols].min(axis=1)

    # Create best performers DataFrames
    best_mse_df = pd.DataFrame({
        'Dataset_PredLen': best_mse.index,
        'Best_Model_MSE': best_mse.str.replace('_MSE', ''),
        'Best_MSE_Value': best_mse_values
    })

    best_mae_df = pd.DataFrame({
        'Dataset_PredLen': best_mae.index,
        'Best_Model_MAE': best_mae.str.replace('_MAE', ''),
        'Best_MAE_Value': best_mae_values
    })

    # Count wins for each model
    mse_wins = best_mse_df['Best_Model_MSE'].value_counts()
    mae_wins = best_mae_df['Best_Model_MAE'].value_counts()

    win_counts = pd.DataFrame({
        'Model': ['TimeMixer', 'CycleNet', 'SOFTS'],
        'MSE_Wins': [mse_wins.get(model, 0) for model in ['TimeMixer', 'CycleNet', 'SOFTS']],
        'MAE_Wins': [mae_wins.get(model, 0) for model in ['TimeMixer', 'CycleNet', 'SOFTS']]
    })
    win_counts['Total_Wins'] = win_counts['MSE_Wins'] + win_counts['MAE_Wins']

    return best_mse_df, best_mae_df, win_counts

def save_results(comparison_df, summary_df, output_dir):
    """
    Save results to CSV and formatted text files.

    Args:
        comparison_df (DataFrame): Main comparison table
        summary_df (DataFrame): Summary statistics table
        output_dir (str): Output directory path
    """
    output_path = Path(output_dir)

    # Analyze best performers
    best_mse_df, best_mae_df, win_counts = analyze_best_performers(comparison_df)

    # Save CSV files with truncated values
    if comparison_df is not None:
        # Apply truncation to all numeric columns before saving
        comparison_df_truncated = comparison_df.copy()
        for col in comparison_df_truncated.columns:
            comparison_df_truncated[col] = comparison_df_truncated[col].apply(truncate_to_3_decimals)
        comparison_df_truncated.to_csv(output_path / 'model_comparison_results.csv')
        print(f"Saved detailed comparison to: {output_path / 'model_comparison_results.csv'}")

    if summary_df is not None:
        summary_df.to_csv(output_path / 'model_summary_statistics.csv', index=False)
        print(f"Saved summary statistics to: {output_path / 'model_summary_statistics.csv'}")

    if best_mse_df is not None and best_mae_df is not None and win_counts is not None:
        best_mse_df.to_csv(output_path / 'best_performers_mse.csv', index=False)
        best_mae_df.to_csv(output_path / 'best_performers_mae.csv', index=False)
        win_counts.to_csv(output_path / 'model_win_counts.csv', index=False)
        print(f"Saved best performers analysis to: {output_path / 'best_performers_*.csv'}")

    # Save beautifully formatted text table
    with open(output_path / 'model_comparison_table.txt', 'w', encoding='utf-8') as f:
        # Enhanced title section
        title_width = 100
        f.write("╔" + "═" * (title_width-2) + "╗\n")
        f.write("║" + " " * ((title_width-30)//2) + "MODEL PERFORMANCE COMPARISON" + " " * ((title_width-30)//2) + "║\n")
        f.write("║" + " " * ((title_width-40)//2) + "Time Series Forecasting Models Analysis" + " " * ((title_width-40)//2) + "║\n")
        f.write("╚" + "═" * (title_width-2) + "╝\n\n")

        if comparison_df is not None:
            f.write("📊 DETAILED PERFORMANCE COMPARISON\n")
            f.write("═" * 50 + "\n")
            f.write("Values are truncated (not rounded) to 3 decimal places\n")
            f.write("* indicates best performance for each dataset-prediction length combination\n\n")

            # Create the beautiful table
            formatted_table = create_beautiful_table(comparison_df)
            f.write(formatted_table)
            f.write("\n\n")

        if summary_df is not None:
            f.write("📈 SUMMARY STATISTICS\n")
            f.write("═" * 25 + "\n")
            # Apply truncation to summary statistics
            summary_truncated = summary_df.copy()
            for col in ['Avg_MSE', 'Avg_MAE', 'Min_MSE', 'Min_MAE', 'Max_MSE', 'Max_MAE']:
                if col in summary_truncated.columns:
                    summary_truncated[col] = summary_truncated[col].apply(truncate_to_3_decimals)

            summary_formatted = summary_truncated.to_string(index=False,
                                                           formatters={
                                                               'Avg_MSE': '{:.3f}'.format,
                                                               'Avg_MAE': '{:.3f}'.format,
                                                               'Min_MSE': '{:.3f}'.format,
                                                               'Min_MAE': '{:.3f}'.format,
                                                               'Max_MSE': '{:.3f}'.format,
                                                               'Max_MAE': '{:.3f}'.format
                                                           })
            f.write(summary_formatted)
            f.write("\n\n")

        if win_counts is not None:
            f.write("🏆 MODEL WIN COUNTS (Best Performance)\n")
            f.write("═" * 40 + "\n")
            f.write(win_counts.to_string(index=False))
            f.write("\n\n")

            # Add insights
            f.write("💡 KEY INSIGHTS\n")
            f.write("═" * 15 + "\n")
            best_overall = win_counts.loc[win_counts['Total_Wins'].idxmax(), 'Model']
            f.write(f"🥇 Best overall performer: {best_overall} ({win_counts.loc[win_counts['Total_Wins'].idxmax(), 'Total_Wins']} total wins)\n")

            best_mse_model = win_counts.loc[win_counts['MSE_Wins'].idxmax(), 'Model']
            best_mae_model = win_counts.loc[win_counts['MAE_Wins'].idxmax(), 'Model']
            f.write(f"📉 Best for MSE: {best_mse_model} ({win_counts.loc[win_counts['MSE_Wins'].idxmax(), 'MSE_Wins']} wins)\n")
            f.write(f"📊 Best for MAE: {best_mae_model} ({win_counts.loc[win_counts['MAE_Wins'].idxmax(), 'MAE_Wins']} wins)\n")

            if summary_df is not None:
                lowest_avg_mse = summary_df.loc[summary_df['Avg_MSE'].idxmin(), 'Model']
                lowest_avg_mae = summary_df.loc[summary_df['Avg_MAE'].idxmin(), 'Model']
                f.write(f"⭐ Lowest average MSE: {lowest_avg_mse} ({truncate_to_3_decimals(summary_df.loc[summary_df['Avg_MSE'].idxmin(), 'Avg_MSE']):.3f})\n")
                f.write(f"⭐ Lowest average MAE: {lowest_avg_mae} ({truncate_to_3_decimals(summary_df.loc[summary_df['Avg_MAE'].idxmin(), 'Avg_MAE']):.3f})\n")

    print(f"Saved formatted table to: {output_path / 'model_comparison_table.txt'}")

    # Focus on creating beautiful text table only - no markdown file needed
    print(f"Beautiful text table saved to: {output_path / 'model_comparison_table.txt'}")

def main():
    """Main function to process all result files and generate comparison tables."""
    
    # Define file paths
    base_dir = Path('AAAI_add')
    result_files = {
        'TimeMixer': base_dir / 'timeMixer_unified_result.txt',
        'CycleNet': base_dir / 'cycleNet_unified_result.txt',
        'SOFTS': base_dir / 'softs_unified_result.txt'
    }
    
    print("Processing experimental results...")
    print("=" * 50)
    
    # Parse all result files
    all_results = []
    for model_name, file_path in result_files.items():
        if file_path.exists():
            print(f"Processing {model_name} results from {file_path}")
            results = parse_result_file(file_path, model_name)
            all_results.extend(results)
            print(f"  Found {len(results)} experiments for {model_name}")
        else:
            print(f"Warning: {file_path} not found!")
    
    print(f"\nTotal experiments processed: {len(all_results)}")
    
    if not all_results:
        print("No results found! Please check the input files.")
        return
    
    # Create comparison tables
    print("\nCreating comparison tables...")
    comparison_df, summary_df = create_comparison_tables(all_results)
    
    # Save results
    print("\nSaving results...")
    save_results(comparison_df, summary_df, base_dir)
    
    # Display summary
    print("\n" + "=" * 50)
    print("ANALYSIS COMPLETE")
    print("=" * 50)
    
    if summary_df is not None:
        print("\nSUMMARY STATISTICS:")
        print(summary_df.to_string(index=False))
    
    print(f"\nOutput files saved to: {base_dir}")
    print("- model_comparison_results.csv (detailed results)")
    print("- model_summary_statistics.csv (summary stats)")
    print("- model_comparison_table.txt (formatted table)")

if __name__ == "__main__":
    main()
