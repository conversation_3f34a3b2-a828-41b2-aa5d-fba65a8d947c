#!/usr/bin/env python3
"""
Script to analyze multi-length prediction experimental results from TimeMixer, CycleNet, and SOFTS models.
Processes result files and creates a structured, parseable output for automated analysis.
"""

import re
import math
from pathlib import Path
from collections import defaultdict

def truncate_to_3_decimals(value):
    """
    Truncate a number to exactly 3 decimal places (no rounding).
    
    Args:
        value (float): Number to truncate
    
    Returns:
        float: Truncated value to 3 decimal places
    """
    if value is None:
        return None
    return math.floor(value * 1000) / 1000

def parse_timemixer_results(file_path):
    """
    Parse TimeMixer results file.
    Format: TimeMixer_ETTh2_sl512_pl720 -> dataset=ETTh2, pred_len=720
    
    Args:
        file_path (str): Path to TimeMixer results file
    
    Returns:
        dict: Parsed results organized by dataset and prediction length
    """
    results = defaultdict(lambda: defaultdict(dict))
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # Look for experiment identifier
            if line.startswith('TimeMixer_'):
                # Parse: TimeMixer_ETTh2_sl512_pl720
                match = re.match(r'TimeMixer_([^_]+)_sl\d+_pl(\d+)', line)
                if match:
                    dataset = match.group(1)
                    pred_len = int(match.group(2))
                    
                    # Look for metrics in next line
                    if i + 1 < len(lines):
                        metrics_line = lines[i + 1].strip()
                        mse_match = re.search(r'mse:([\d.]+)', metrics_line)
                        mae_match = re.search(r'mae:([\d.]+)', metrics_line)
                        
                        if mse_match and mae_match:
                            mse = truncate_to_3_decimals(float(mse_match.group(1)))
                            mae = truncate_to_3_decimals(float(mae_match.group(1)))
                            
                            results[dataset][pred_len] = {
                                'mse': mse,
                                'mae': mae,
                                'experiment_id': line
                            }
            i += 1
    
    except Exception as e:
        print(f"Error parsing TimeMixer results: {e}")
    
    return results

def parse_cyclenet_results(file_path):
    """
    Parse CycleNet results file.
    Format: ETTh1_mlp_336_192 -> dataset=ETTh1, pred_len=192 (ignore 336)
    
    Args:
        file_path (str): Path to CycleNet results file
    
    Returns:
        dict: Parsed results organized by dataset and prediction length
    """
    results = defaultdict(lambda: defaultdict(dict))
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # Look for experiment identifier
            if '_mlp_' in line:
                # Parse: ETTh1_mlp_336_192 -> dataset=ETTh1, pred_len=192
                match = re.match(r'([^_]+)_mlp_\d+_(\d+)', line)
                if match:
                    dataset = match.group(1)
                    pred_len = int(match.group(2))
                    
                    # Look for metrics in next line
                    if i + 1 < len(lines):
                        metrics_line = lines[i + 1].strip()
                        mse_match = re.search(r'mse:([\d.]+)', metrics_line)
                        mae_match = re.search(r'mae:([\d.]+)', metrics_line)
                        
                        if mse_match and mae_match:
                            mse = truncate_to_3_decimals(float(mse_match.group(1)))
                            mae = truncate_to_3_decimals(float(mae_match.group(1)))
                            
                            results[dataset][pred_len] = {
                                'mse': mse,
                                'mae': mae,
                                'experiment_id': line
                            }
            i += 1
    
    except Exception as e:
        print(f"Error parsing CycleNet results: {e}")
    
    return results

def parse_softs_results(file_path):
    """
    Parse SOFTS results file.
    Format: SOFTS_exchange_sl96_pl192 -> dataset=exchange, pred_len=192 (ignore sl96)
    
    Args:
        file_path (str): Path to SOFTS results file
    
    Returns:
        dict: Parsed results organized by dataset and prediction length
    """
    results = defaultdict(lambda: defaultdict(dict))
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # Look for experiment identifier
            if line.startswith('SOFTS_'):
                # Parse: SOFTS_exchange_sl96_pl192 -> dataset=exchange, pred_len=192
                match = re.match(r'SOFTS_([^_]+)_sl\d+_pl(\d+)', line)
                if match:
                    dataset = match.group(1)
                    pred_len = int(match.group(2))
                    
                    # Look for metrics in next line
                    if i + 1 < len(lines):
                        metrics_line = lines[i + 1].strip()
                        mse_match = re.search(r'mse:([\d.]+)', metrics_line)
                        mae_match = re.search(r'mae:([\d.]+)', metrics_line)
                        
                        if mse_match and mae_match:
                            mse = truncate_to_3_decimals(float(mse_match.group(1)))
                            mae = truncate_to_3_decimals(float(mae_match.group(1)))
                            
                            results[dataset][pred_len] = {
                                'mse': mse,
                                'mae': mae,
                                'experiment_id': line
                            }
            i += 1
    
    except Exception as e:
        print(f"Error parsing SOFTS results: {e}")
    
    return results

def create_structured_output(timemixer_results, cyclenet_results, softs_results, output_path):
    """
    Create a structured, parseable output file.
    
    Args:
        timemixer_results (dict): TimeMixer results
        cyclenet_results (dict): CycleNet results
        softs_results (dict): SOFTS results
        output_path (str): Output file path
    """
    # Collect all datasets and prediction lengths
    all_datasets = set()
    all_pred_lens = set()
    
    for results in [timemixer_results, cyclenet_results, softs_results]:
        for dataset in results:
            all_datasets.add(dataset)
            for pred_len in results[dataset]:
                all_pred_lens.add(pred_len)
    
    # Sort for consistent output
    sorted_datasets = sorted(all_datasets)
    sorted_pred_lens = sorted(all_pred_lens)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        # Header
        f.write("=" * 80 + "\n")
        f.write("MULTI-LENGTH PREDICTION RESULTS ANALYSIS\n")
        f.write("Time Series Forecasting Models Performance\n")
        f.write("=" * 80 + "\n\n")
        
        f.write("FORMAT: Model -> Dataset -> Prediction_Length -> MSE/MAE\n")
        f.write("VALUES: Truncated to 3 decimal places (not rounded)\n")
        f.write("MODELS: TimeMixer, CycleNet, SOFTS\n\n")
        
        # Results by model
        models_data = {
            'TimeMixer': timemixer_results,
            'CycleNet': cyclenet_results,
            'SOFTS': softs_results
        }
        
        for model_name, model_results in models_data.items():
            f.write(f"[MODEL: {model_name}]\n")
            f.write("-" * 40 + "\n")
            
            if not model_results:
                f.write("No results found\n\n")
                continue
            
            for dataset in sorted_datasets:
                if dataset in model_results:
                    f.write(f"  [DATASET: {dataset}]\n")
                    
                    for pred_len in sorted_pred_lens:
                        if pred_len in model_results[dataset]:
                            metrics = model_results[dataset][pred_len]
                            f.write(f"    PRED_LEN_{pred_len}: MSE={metrics['mse']:.3f}, MAE={metrics['mae']:.3f}\n")
                    f.write("\n")
            f.write("\n")
        
        # Summary statistics
        f.write("=" * 80 + "\n")
        f.write("SUMMARY STATISTICS\n")
        f.write("=" * 80 + "\n\n")
        
        for model_name, model_results in models_data.items():
            f.write(f"[{model_name} SUMMARY]\n")
            
            all_mse = []
            all_mae = []
            experiment_count = 0
            
            for dataset in model_results:
                for pred_len in model_results[dataset]:
                    metrics = model_results[dataset][pred_len]
                    all_mse.append(metrics['mse'])
                    all_mae.append(metrics['mae'])
                    experiment_count += 1
            
            if all_mse and all_mae:
                avg_mse = truncate_to_3_decimals(sum(all_mse) / len(all_mse))
                avg_mae = truncate_to_3_decimals(sum(all_mae) / len(all_mae))
                min_mse = truncate_to_3_decimals(min(all_mse))
                min_mae = truncate_to_3_decimals(min(all_mae))
                max_mse = truncate_to_3_decimals(max(all_mse))
                max_mae = truncate_to_3_decimals(max(all_mae))
                
                f.write(f"  Experiments: {experiment_count}\n")
                f.write(f"  Average MSE: {avg_mse:.3f}\n")
                f.write(f"  Average MAE: {avg_mae:.3f}\n")
                f.write(f"  Min MSE: {min_mse:.3f}\n")
                f.write(f"  Min MAE: {min_mae:.3f}\n")
                f.write(f"  Max MSE: {max_mse:.3f}\n")
                f.write(f"  Max MAE: {max_mae:.3f}\n")
            else:
                f.write("  No valid results found\n")
            f.write("\n")

def main():
    """Main function to process all result files and generate structured output."""
    
    # Define file paths
    base_dir = Path('AAAI_add')
    result_files = {
        'TimeMixer': base_dir / 'timeMixer_multiLen_result.txt',
        'CycleNet': base_dir / 'cycleNet_multiLen_result.txt',
        'SOFTS': base_dir / 'softs_multiLen_result.txt'
    }
    
    print("Processing multi-length prediction results...")
    print("=" * 50)
    
    # Parse all result files
    timemixer_results = parse_timemixer_results(result_files['TimeMixer'])
    cyclenet_results = parse_cyclenet_results(result_files['CycleNet'])
    softs_results = parse_softs_results(result_files['SOFTS'])
    
    # Print parsing summary
    print(f"TimeMixer: {sum(len(dataset_results) for dataset_results in timemixer_results.values())} experiments")
    print(f"CycleNet: {sum(len(dataset_results) for dataset_results in cyclenet_results.values())} experiments")
    print(f"SOFTS: {sum(len(dataset_results) for dataset_results in softs_results.values())} experiments")
    
    # Create structured output
    output_file = base_dir / 'multiLen_results_structured.txt'
    create_structured_output(timemixer_results, cyclenet_results, softs_results, output_file)
    
    print(f"\nStructured output saved to: {output_file}")
    print("File is optimized for automated parsing and analysis")

if __name__ == "__main__":
    main()
