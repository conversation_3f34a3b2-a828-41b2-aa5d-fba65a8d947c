#!/usr/bin/env python3
"""
Script to process ablation experiment results from MyModel_ablation_weak_signal.txt
Reorganizes data into a GPT-readable format with averages across prediction lengths.
"""

import re
import os
from collections import defaultdict
from typing import Dict, List, Tuple

def parse_experiment_name(name: str) -> Tuple[str, str, str, str, str]:
    """
    Parse experiment name to extract components.
    Format: MyModel_{dataset}_sl{input_length}_pl{prediction_length}_{ablation_component}
    
    Returns:
        Tuple of (model, dataset, input_length, prediction_length, ablation_component)
    """
    pattern = r'MyModel_([^_]+)_sl(\d+)_pl(\d+)_(.+)'
    match = re.match(pattern, name)
    if match:
        dataset, input_length, prediction_length, ablation_component = match.groups()
        return "MyModel", dataset, input_length, prediction_length, ablation_component
    else:
        raise ValueError(f"Cannot parse experiment name: {name}")

def parse_metrics(line: str) -> <PERSON><PERSON>[float, float]:
    """
    Parse MSE and MAE from metrics line.
    Format: mse:0.28436291217803955, mae:0.3313014507293701
    
    Returns:
        Tuple of (mse, mae)
    """
    mse_match = re.search(r'mse:([\d.]+)', line)
    mae_match = re.search(r'mae:([\d.]+)', line)
    
    if mse_match and mae_match:
        return float(mse_match.group(1)), float(mae_match.group(1))
    else:
        raise ValueError(f"Cannot parse metrics from line: {line}")

def process_ablation_results(input_file: str) -> Dict:
    """
    Process the ablation results file and organize data by dataset and ablation component.
    
    Returns:
        Dictionary with organized results
    """
    results = defaultdict(lambda: defaultdict(lambda: {'mse': [], 'mae': []}))
    
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Skip empty lines
        if not line:
            i += 1
            continue
            
        # Check if this is an experiment name line
        if line.startswith('MyModel_'):
            try:
                model, dataset, input_length, prediction_length, ablation_component = parse_experiment_name(line)
                
                # Next line should contain metrics
                if i + 1 < len(lines):
                    metrics_line = lines[i + 1].strip()
                    if 'mse:' in metrics_line and 'mae:' in metrics_line:
                        mse, mae = parse_metrics(metrics_line)
                        
                        # Store results
                        results[dataset][ablation_component]['mse'].append(mse)
                        results[dataset][ablation_component]['mae'].append(mae)
                        
                        i += 2  # Skip the metrics line
                    else:
                        i += 1
                else:
                    i += 1
            except ValueError as e:
                print(f"Warning: {e}")
                i += 1
        else:
            i += 1
    
    return results

def calculate_averages(results: Dict) -> Dict:
    """
    Calculate averages across prediction lengths for each dataset and ablation component.
    """
    averaged_results = {}
    
    for dataset in results:
        averaged_results[dataset] = {}
        for ablation_component in results[dataset]:
            mse_values = results[dataset][ablation_component]['mse']
            mae_values = results[dataset][ablation_component]['mae']
            
            if mse_values and mae_values:
                avg_mse = sum(mse_values) / len(mse_values)
                avg_mae = sum(mae_values) / len(mae_values)
                
                averaged_results[dataset][ablation_component] = {
                    'avg_mse': avg_mse,
                    'avg_mae': avg_mae,
                    'count': len(mse_values)
                }
    
    return averaged_results

def format_number(value: float) -> str:
    """Format number to exactly 3 decimal places (truncated, not rounded)."""
    return f"{int(value * 1000) / 1000:.3f}"

def generate_structured_output(averaged_results: Dict) -> str:
    """
    Generate structured output for GPT-readable format.
    """
    output = []
    output.append("=" * 80)
    output.append("ABLATION STUDY RESULTS - AVERAGED ACROSS PREDICTION LENGTHS")
    output.append("=" * 80)
    output.append("")
    
    # Get all datasets and ablation components
    datasets = sorted(averaged_results.keys())
    all_components = set()
    for dataset in datasets:
        all_components.update(averaged_results[dataset].keys())
    ablation_components = sorted(all_components)
    
    output.append("DATASETS ANALYZED:")
    for i, dataset in enumerate(datasets, 1):
        output.append(f"  {i}. {dataset}")
    output.append("")
    
    output.append("ABLATION COMPONENTS:")
    for i, component in enumerate(ablation_components, 1):
        output.append(f"  {i}. {component}")
    output.append("")
    
    # Generate detailed results for each dataset
    output.append("DETAILED RESULTS BY DATASET:")
    output.append("-" * 50)
    
    for dataset in datasets:
        output.append(f"\nDATASET: {dataset}")
        output.append("-" * 30)
        
        if dataset in averaged_results:
            for component in ablation_components:
                if component in averaged_results[dataset]:
                    data = averaged_results[dataset][component]
                    mse_str = format_number(data['avg_mse'])
                    mae_str = format_number(data['avg_mae'])
                    output.append(f"  {component:20} | MSE: {mse_str} | MAE: {mae_str} | (n={data['count']})")
                else:
                    output.append(f"  {component:20} | No data available")
        else:
            output.append("  No data available for this dataset")
    
    # Generate comparison table
    output.append("\n" + "=" * 80)
    output.append("COMPARISON TABLE - MSE VALUES")
    output.append("=" * 80)
    
    # Header
    header = f"{'Dataset':<12}"
    for component in ablation_components:
        header += f" | {component:<15}"
    output.append(header)
    output.append("-" * len(header))
    
    # Data rows
    for dataset in datasets:
        row = f"{dataset:<12}"
        for component in ablation_components:
            if dataset in averaged_results and component in averaged_results[dataset]:
                mse_val = format_number(averaged_results[dataset][component]['avg_mse'])
                row += f" | {mse_val:<15}"
            else:
                row += f" | {'N/A':<15}"
        output.append(row)
    
    output.append("\n" + "=" * 80)
    output.append("COMPARISON TABLE - MAE VALUES")
    output.append("=" * 80)
    
    # Header
    header = f"{'Dataset':<12}"
    for component in ablation_components:
        header += f" | {component:<15}"
    output.append(header)
    output.append("-" * len(header))
    
    # Data rows
    for dataset in datasets:
        row = f"{dataset:<12}"
        for component in ablation_components:
            if dataset in averaged_results and component in averaged_results[dataset]:
                mae_val = format_number(averaged_results[dataset][component]['avg_mae'])
                row += f" | {mae_val:<15}"
            else:
                row += f" | {'N/A':<15}"
        output.append(row)
    
    # Generate summary statistics
    output.append("\n" + "=" * 80)
    output.append("SUMMARY STATISTICS")
    output.append("=" * 80)
    
    for component in ablation_components:
        output.append(f"\nABLATION COMPONENT: {component}")
        output.append("-" * 40)
        
        mse_values = []
        mae_values = []
        
        for dataset in datasets:
            if dataset in averaged_results and component in averaged_results[dataset]:
                mse_values.append(averaged_results[dataset][component]['avg_mse'])
                mae_values.append(averaged_results[dataset][component]['avg_mae'])
        
        if mse_values and mae_values:
            avg_mse_across_datasets = sum(mse_values) / len(mse_values)
            avg_mae_across_datasets = sum(mae_values) / len(mae_values)
            
            output.append(f"  Average MSE across datasets: {format_number(avg_mse_across_datasets)}")
            output.append(f"  Average MAE across datasets: {format_number(avg_mae_across_datasets)}")
            output.append(f"  Number of datasets: {len(mse_values)}")
        else:
            output.append("  No data available")
    
    return "\n".join(output)

def main():
    """Main function to process ablation results."""
    input_file = "MyModel_ablation_weak_signal.txt"
    output_file = "processed_ablation_results.txt"
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found!")
        return
    
    print(f"Processing ablation results from '{input_file}'...")
    
    # Process the results
    results = process_ablation_results(input_file)
    averaged_results = calculate_averages(results)
    
    # Generate structured output
    structured_output = generate_structured_output(averaged_results)
    
    # Save to output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(structured_output)
    
    print(f"Results processed and saved to '{output_file}'")
    print(f"Found {len(averaged_results)} datasets with ablation results")
    
    # Print summary to console
    print("\nSUMMARY:")
    for dataset in sorted(averaged_results.keys()):
        components = list(averaged_results[dataset].keys())
        print(f"  {dataset}: {len(components)} ablation components")

if __name__ == "__main__":
    main()
