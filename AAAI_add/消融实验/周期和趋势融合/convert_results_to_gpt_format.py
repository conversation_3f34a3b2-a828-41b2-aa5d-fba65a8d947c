#!/usr/bin/env python3
"""
Script to convert experimental results from result_ablation_attn_no2.txt 
into a GPT-readable format.

This script parses model experimental results and formats them in a structured way
that is easy for GPT to understand and compare.
"""

import re
import math
from collections import defaultdict
from typing import Dict, List, Tuple


def truncate_to_3_decimals(value: float) -> str:
    """
    Truncate a float value to exactly 3 decimal places (not round).
    
    Args:
        value: Float value to truncate
        
    Returns:
        String representation of the truncated value
    """
    # Multiply by 1000, truncate to integer, then divide by 1000
    truncated = math.floor(value * 1000) / 1000
    return f"{truncated:.3f}"


def parse_filename(filename: str) -> Tuple[str, str, int]:
    """
    Parse model name, dataset, and prediction length from filename.
    
    Args:
        filename: Filename in format like "xPatch_ETTh1_sl96_pl192_mlp"
        
    Returns:
        Tuple of (model_name, dataset, prediction_length)
    """
    parts = filename.split('_')
    model_name = parts[0]
    dataset = parts[1]
    
    # Find prediction length (pl parameter)
    prediction_length = None
    for part in parts:
        if part.startswith('pl'):
            prediction_length = int(part[2:])  # Remove 'pl' prefix
            break
    
    return model_name, dataset, prediction_length


def parse_results_file(filepath: str) -> Dict:
    """
    Parse the experimental results file.
    
    Args:
        filepath: Path to the results file
        
    Returns:
        Dictionary containing parsed results organized by model, dataset, and prediction length
    """
    results = defaultdict(lambda: defaultdict(lambda: defaultdict(dict)))
    
    with open(filepath, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Skip empty lines
        if not line:
            i += 1
            continue
            
        # Check if this line contains a model experiment name
        if any(model in line for model in ['MyModel_', 'xPatch_', 'Amplifier_']):
            experiment_name = line
            
            # Parse the experiment name
            model_name, dataset, prediction_length = parse_filename(experiment_name)
            
            # Look for the next line with MSE and MAE values
            i += 1
            if i < len(lines):
                metrics_line = lines[i].strip()
                
                # Parse MSE and MAE values using regex
                mse_match = re.search(r'mse:([\d.]+)', metrics_line)
                mae_match = re.search(r'mae:([\d.]+)', metrics_line)
                
                if mse_match and mae_match:
                    mse_value = float(mse_match.group(1))
                    mae_value = float(mae_match.group(1))
                    
                    # Store results with truncated values
                    results[model_name][dataset][prediction_length] = {
                        'mse': truncate_to_3_decimals(mse_value),
                        'mae': truncate_to_3_decimals(mae_value)
                    }
        
        i += 1
    
    return results


def format_results_for_gpt(results: Dict) -> str:
    """
    Format the parsed results into a GPT-readable format.
    
    Args:
        results: Dictionary containing parsed results
        
    Returns:
        Formatted string ready for GPT consumption
    """
    output_lines = []
    output_lines.append("# Experimental Results Summary")
    output_lines.append("# Format: Model | Dataset | Prediction_Length | MSE | MAE")
    output_lines.append("# All MSE and MAE values are truncated to 3 decimal places")
    output_lines.append("")
    
    # Sort models for consistent output
    for model_name in sorted(results.keys()):
        output_lines.append(f"## {model_name} Results")
        output_lines.append("")
        
        # Sort datasets for consistent output
        for dataset in sorted(results[model_name].keys()):
            output_lines.append(f"### Dataset: {dataset}")
            
            # Sort prediction lengths for consistent output
            for pred_length in sorted(results[model_name][dataset].keys()):
                metrics = results[model_name][dataset][pred_length]
                output_lines.append(
                    f"{model_name} | {dataset} | {pred_length} | {metrics['mse']} | {metrics['mae']}"
                )
            
            output_lines.append("")
    
    # Add a summary table for easy comparison
    output_lines.append("## Summary Table for GPT Analysis")
    output_lines.append("")
    output_lines.append("```")
    output_lines.append("Model_Dataset_PredLength | MSE | MAE")
    output_lines.append("-" * 50)
    
    # Create a comprehensive comparison table
    all_experiments = []
    for model_name in sorted(results.keys()):
        for dataset in sorted(results[model_name].keys()):
            for pred_length in sorted(results[model_name][dataset].keys()):
                metrics = results[model_name][dataset][pred_length]
                experiment_id = f"{model_name}_{dataset}_{pred_length}"
                all_experiments.append((experiment_id, metrics['mse'], metrics['mae']))
    
    # Sort by experiment ID for consistent ordering
    for exp_id, mse, mae in sorted(all_experiments):
        output_lines.append(f"{exp_id} | {mse} | {mae}")
    
    output_lines.append("```")
    output_lines.append("")
    
    # Add metadata for GPT understanding
    output_lines.append("## Metadata for GPT Analysis")
    output_lines.append(f"- Total Models: {len(results)}")
    output_lines.append(f"- Models: {', '.join(sorted(results.keys()))}")
    
    all_datasets = set()
    all_pred_lengths = set()
    for model_data in results.values():
        for dataset, pred_data in model_data.items():
            all_datasets.add(dataset)
            all_pred_lengths.update(pred_data.keys())
    
    output_lines.append(f"- Datasets: {', '.join(sorted(all_datasets))}")
    output_lines.append(f"- Prediction Lengths: {', '.join(map(str, sorted(all_pred_lengths)))}")
    output_lines.append("- Metrics: MSE (Mean Squared Error), MAE (Mean Absolute Error)")
    output_lines.append("- All values truncated to 3 decimal places for consistency")
    
    return '\n'.join(output_lines)


def main():
    """Main function to execute the conversion."""
    input_file = "AAAI_add/消融实验/result_ablation_attn_no2.txt"
    output_file = "AAAI_add/消融实验/experimental_results_gpt_format.txt"
    
    try:
        # Parse the results file
        print(f"Parsing results from: {input_file}")
        results = parse_results_file(input_file)
        
        # Format for GPT
        print("Formatting results for GPT...")
        formatted_output = format_results_for_gpt(results)
        
        # Write to output file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(formatted_output)
        
        print(f"Conversion completed! Output saved to: {output_file}")
        
        # Print summary statistics
        total_experiments = sum(
            len(pred_data) 
            for model_data in results.values() 
            for pred_data in model_data.values()
        )
        print(f"Total experiments processed: {total_experiments}")
        print(f"Models found: {', '.join(sorted(results.keys()))}")
        
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found.")
    except Exception as e:
        print(f"Error during conversion: {str(e)}")


if __name__ == "__main__":
    main()
