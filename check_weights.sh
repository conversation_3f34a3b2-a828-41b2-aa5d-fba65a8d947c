#!/bin/bash
# 模型权重检查脚本 - 验证所有必需的模型权重文件是否存在

echo "===========================================" 
echo "时间序列预测模型权重检查脚本"
echo "===========================================" 

# 模型权重存储路径
CHECKPOINTS_PATH="./checkpoints"

# 模型名称列表
MODELS=("xPatch" "DLinear" "MyModel" "FITS" "iTransformer" "Amplifier")

# 数据集名称列表
DATASETS=("ETTh1" "ETTh2" "ETTm1" "ETTm2" "electricity" "exchange" "traffic" "weather" "solar")

# 预测长度列表
PRED_LENGTHS=(96 192 336 720)

# 创建权重目录（如果不存在）
if [ ! -d "$CHECKPOINTS_PATH" ]; then
    echo "权重目录不存在，创建 $CHECKPOINTS_PATH"
    mkdir -p "$CHECKPOINTS_PATH"
fi

# 检查各个模型的权重
echo "开始检查模型权重..."
missing_weights=0
total_weights=0

for model in "${MODELS[@]}"; do
    echo "检查 $model 模型权重..."
    model_missing=0
    model_total=0
    
    for dataset in "${DATASETS[@]}"; do
        for pred_len in "${PRED_LENGTHS[@]}"; do
            # 构建模型ID，与训练时一致
            ma_type="ema"
            
            # 为不同模型构建不同的ID格式
            if [[ "$model" == "xPatch" || "$model" == "DLinear" ]]; then
                model_id="${dataset}_${pred_len}_${ma_type}"
            else
                data_type="custom"
                if [[ "$dataset" == "ETT"* ]]; then
                    data_type="$dataset"
                elif [[ "$dataset" == "solar" ]]; then
                    data_type="Solar"
                fi
                model_id="${dataset}_${pred_len}_${ma_type}_${model}_${data_type}_ftM_sl96_ll48_pl${pred_len}_Exp_0"
            fi
            
            # 权重文件路径
            weight_path="$CHECKPOINTS_PATH/$model_id"
            
            # 检查目录是否存在
            if [ -d "$weight_path" ]; then
                # 检查是否包含模型权重文件
                if [ -f "$weight_path/best_model.pth" ]; then
                    echo "✓ $model / $dataset / $pred_len 权重存在"
                else
                    echo "✗ $model / $dataset / $pred_len 权重文件不存在"
                    missing_weights=$((missing_weights + 1))
                    model_missing=$((model_missing + 1))
                fi
            else
                echo "✗ $model / $dataset / $pred_len 权重目录不存在"
                missing_weights=$((missing_weights + 1))
                model_missing=$((model_missing + 1))
            fi
            
            total_weights=$((total_weights + 1))
            model_total=$((model_total + 1))
        done
    done
    
    # 显示该模型的统计信息
    echo "$model 模型: 缺失 $model_missing / $model_total 个权重文件"
    echo "--------------------------------------------"
done

# 总结结果
echo "===========================================" 
if [ $missing_weights -eq 0 ]; then
    echo "所有模型权重检查完成，全部存在。"
else
    echo "警告: 共缺失 $missing_weights / $total_weights 个模型权重文件"
    echo ""
    echo "请确保已经训练所有必需的模型并保存权重文件。"
    echo "可能需要运行训练脚本来生成缺失的权重文件。"
fi
echo "===========================================" 