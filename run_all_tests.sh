#!/bin/bash
echo "===========================================" 
echo "时间序列预测模型批量测试脚本"
echo "===========================================" 

# 创建所需目录
if [ ! -d "./test_plots" ]; then
    mkdir ./test_plots
fi

if [ ! -d "./scripts/test" ]; then
    mkdir -p ./scripts/test
fi

# 确保脚本可执行
chmod +x scripts/test/test_*.sh

# 检查测试脚本是否存在，如果不存在则提示错误
test_scripts=(
    "scripts/test/test_xPatch.sh"
    "scripts/test/test_DLinear.sh"
    "scripts/test/test_MyModel.sh"
    "scripts/test/test_FITS.sh"
    "scripts/test/test_iTransformer.sh"
    "scripts/test/test_Amplifier.sh"
)

for script in "${test_scripts[@]}"; do
    if [ ! -f "$script" ]; then
        echo "错误: 测试脚本 $script 不存在!"
        exit 1
    fi
done

# 依次运行各模型的测试脚本
echo "开始运行 xPatch 模型测试..."
./scripts/test/test_xPatch.sh
echo "xPatch 模型测试完成"

echo "开始运行 DLinear 模型测试..."
./scripts/test/test_DLinear.sh
echo "DLinear 模型测试完成"

echo "开始运行 MyModel 模型测试..."
./scripts/test/test_MyModel.sh
echo "MyModel 模型测试完成"

echo "开始运行 FITS 模型测试..."
./scripts/test/test_FITS.sh
echo "FITS 模型测试完成"

echo "开始运行 iTransformer 模型测试..."
./scripts/test/test_iTransformer.sh
echo "iTransformer 模型测试完成"

echo "开始运行 Amplifier 模型测试..."
./scripts/test/test_Amplifier.sh
echo "Amplifier 模型测试完成"

echo "===========================================" 
echo "所有模型测试完成，结果保存在test_plots目录下"
echo "===========================================" 