#!/bin/bash
# 数据集检查脚本 - 验证所有必需的数据集是否存在且格式正确

echo "===========================================" 
echo "时间序列预测数据集检查脚本"
echo "===========================================" 

# 数据集路径
DATASET_PATH="./dataset"

# 需要检查的数据集文件
DATASETS=(
    "ETTh1.csv"
    "ETTh2.csv" 
    "ETTm1.csv"
    "ETTm2.csv"
    "electricity.csv"
    "exchange_rate.csv"
    "traffic.csv"
    "weather.csv"
    "solar.txt"
)

# 创建数据集目录（如果不存在）
if [ ! -d "$DATASET_PATH" ]; then
    echo "数据集目录不存在，创建 $DATASET_PATH"
    mkdir -p "$DATASET_PATH"
    mkdir -p "$DATASET_PATH/ETT-small"
    mkdir -p "$DATASET_PATH/electricity"
    echo "已创建必要的数据集目录"
fi

# 检查每个数据集
echo "开始检查数据集..."
missing_datasets=()

check_dataset() {
    local dataset=$1
    local path="$DATASET_PATH/$dataset"
    
    # 处理特殊路径
    if [[ "$dataset" == ETT* ]]; then
        path="$DATASET_PATH/ETT-small/$dataset"
    elif [[ "$dataset" == "electricity.csv" ]]; then
        path="$DATASET_PATH/electricity/$dataset"
    fi
    
    if [ -f "$path" ]; then
        echo "✓ $dataset 存在"
        
        # 检查文件大小
        size=$(du -h "$path" | cut -f1)
        echo "  文件大小: $size"
        
        # 检查文件格式（头部几行）
        echo "  文件头部预览:"
        head -n 3 "$path" | sed 's/^/    /'
        
        # 检查行数
        lines=$(wc -l < "$path")
        echo "  总行数: $lines"
        echo ""
    else
        echo "✗ $dataset 不存在"
        missing_datasets+=("$dataset")
        echo ""
    fi
}

# 检查所有数据集
for dataset in "${DATASETS[@]}"; do
    check_dataset "$dataset"
done

# 总结结果
echo "===========================================" 
if [ ${#missing_datasets[@]} -eq 0 ]; then
    echo "所有数据集检查完成，全部存在。"
else
    echo "警告: 以下 ${#missing_datasets[@]} 个数据集缺失:"
    for missing in "${missing_datasets[@]}"; do
        echo "  - $missing"
    done
    echo ""
    echo "请确保下载所有必需的数据集并放置在正确的位置。"
    echo "建议访问项目的官方库获取数据集下载说明。"
fi
echo "===========================================" 