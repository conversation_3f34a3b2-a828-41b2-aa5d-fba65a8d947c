import numpy as np
import matplotlib.pyplot as plt
from scipy.fft import rfft, rfftfreq
from scipy import signal

def multiscale_windowed_fft(x, window_sizes, overlap_ratio=0.5, window_type='hann'):
    """
    对时间序列执行多尺度窗口FFT分析
    
    参数:
    x: 输入时间序列，一维numpy数组
    window_sizes: 整数列表，表示不同的窗口大小
    overlap_ratio: 滑动窗口的重叠比例，范围[0,1)，默认0.5
    window_type: 窗口函数类型，默认'hann'
    
    返回:
    字典，键为窗口大小，值为包含以下内容的字典:
        - freq: 频率数组
        - fft_segments: 每个窗口的FFT结果列表
        - fft_times: 每个窗口中心对应的时间点
        - spectrogram: 频谱图数据
    """
    results = {}
    
    for window_size in window_sizes:
        # 计算步长（基于重叠率）
        step = int(window_size * (1 - overlap_ratio))
        
        # 计算分段数
        n_segments = (len(x) - window_size) // step + 1
        
        # 如果数据不够一个完整窗口
        if n_segments <= 0:
            print(f"警告: 窗口大小 {window_size} 大于数据长度 {len(x)}，跳过")
            continue
        
        # 创建窗口函数
        window = getattr(signal.windows, window_type)(window_size)
        
        # 存储每个窗口的FFT结果
        fft_segments = []
        fft_times = []
        
        for i in range(n_segments):
            # 计算窗口的起始和结束索引
            start = i * step
            end = start + window_size
            
            # 提取片段
            segment = x[start:end]
            
            # 应用窗口函数
            segment_windowed = segment * window
            
            # 计算FFT
            fft_result = rfft(segment_windowed)
            
            # 计算该窗口中心对应的时间点
            center_time = start + window_size // 2
            
            # 存储结果
            fft_segments.append(fft_result)
            fft_times.append(center_time)
        
        # 计算频率轴
        freqs = rfftfreq(window_size, d=1)  # 假设采样率为1，可根据需要调整
        
        # 创建时间-频率二维数组（频谱图数据）
        spectrogram = np.zeros((len(freqs), n_segments), dtype=complex)
        for i, fft_segment in enumerate(fft_segments):
            spectrogram[:, i] = fft_segment
        
        # 存储结果
        results[window_size] = {
            'freq': freqs,
            'fft_segments': fft_segments,
            'fft_times': fft_times,
            'spectrogram': spectrogram
        }
    
    return results

def plot_multiscale_spectrograms(x, results, sampling_rate=1.0):
    """
    绘制多尺度窗口FFT分析的结果
    
    参数:
    x: 原始时间序列
    results: multiscale_windowed_fft函数的输出
    sampling_rate: 采样率，默认为1.0
    """
    n_windows = len(results)
    if n_windows == 0:
        print("没有可显示的结果")
        return
    
    # 计算时间轴
    t = np.arange(len(x)) / sampling_rate
    
    # 创建图形
    fig = plt.figure(figsize=(12, 3 + 4 * n_windows))
    
    # 在顶部绘制原始信号
    ax_signal = plt.subplot(n_windows + 1, 1, 1)
    ax_signal.plot(t, x)
    ax_signal.set_title('原始时间序列')
    ax_signal.set_xlabel('时间')
    ax_signal.set_ylabel('幅度')
    
    # 绘制不同窗口大小的频谱图
    for i, (window_size, data) in enumerate(sorted(results.items())):
        ax = plt.subplot(n_windows + 1, 1, i + 2)
        
        # 转换为幅度（取模）
        magnitude = np.abs(data['spectrogram'])
        
        # 使用分贝刻度改善可视化
        magnitude_db = 20 * np.log10(magnitude + 1e-10)  # 添加小值避免log(0)
        
        # 计算每个窗口中心的时间
        times = np.array(data['fft_times']) / sampling_rate
        
        # 绘制频谱图
        im = ax.pcolormesh(times, data['freq'] * sampling_rate, magnitude_db, shading='gouraud', cmap='viridis')
        
        # 设置标题和标签
        ax.set_title(f'窗口大小 = {window_size} 的频谱图')
        ax.set_xlabel('时间')
        ax.set_ylabel('频率')
        
        # 添加颜色条
        plt.colorbar(im, ax=ax, label='幅度 (dB)')
    
    plt.tight_layout()
    return fig

# 示例：生成测试数据并执行多尺度窗口FFT分析
def example_usage():
    # 设置参数
    fs = 100  # 采样率（每秒100个样本）
    t = np.arange(0, 10, 1/fs)  # 10秒的时间序列
    
    # 创建一个随时间变化的信号
    # - 0-3秒: 5Hz正弦波
    # - 3-6秒: 15Hz正弦波
    # - 6-10秒: 5Hz和20Hz的混合
    x = np.zeros_like(t)
    x[t < 3] = np.sin(2 * np.pi * 5 * t[t < 3])
    x[(t >= 3) & (t < 6)] = np.sin(2 * np.pi * 15 * t[(t >= 3) & (t < 6)])
    x[t >= 6] = 0.5 * np.sin(2 * np.pi * 5 * t[t >= 6]) + 0.5 * np.sin(2 * np.pi * 20 * t[t >= 6])
    
    # 添加少量随机噪声
    x += 0.1 * np.random.randn(len(x))
    
    # 定义窗口大小（以样本数计）
    window_sizes = [64, 128, 256, 512]
    
    # 执行多尺度窗口FFT分析
    results = multiscale_windowed_fft(x, window_sizes, overlap_ratio=0.75)
    
    # 绘制结果
    plot_multiscale_spectrograms(x, results, sampling_rate=fs)
    plt.show()
    
    return x, results

# 运行示例（取消注释以执行）
x, results = example_usage()
