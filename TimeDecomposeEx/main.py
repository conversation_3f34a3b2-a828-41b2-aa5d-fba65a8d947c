import numpy as np
import pandas as pd
import matplotlib

# 设置非交互式后端，避免图形显示问题
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from scipy import fftpack
from statsmodels.tsa.seasonal import STL

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题


def decompose_time_series(time_series, ma_window=None, num_periods=3):
    """
    使用移动平均 + FFT + MSTL方法对时间序列进行分解

    参数:
    time_series: pandas.Series - 带有日期索引的时间序列数据
    ma_window: int - 移动平均窗口大小，如果为None则自动确定
    num_periods: int - 要保留的主要周期数量

    返回:
    dict - 包含各种分解成分的字典
    """
    # 1. 自动确定移动平均窗口大小（如果未指定）
    if ma_window is None:
        # 使用自相关确定窗口大小
        from statsmodels.tsa.stattools import acf
        acf_values = acf(time_series, nlags=min(len(time_series) // 2, 1000))
        # 寻找自相关的第一个显著下降点
        threshold = max(0.2, acf_values[1] * 0.5)
        for i, val in enumerate(acf_values[1:], 1):
            if val < threshold:
                ma_window = max(i - 1, 7)  # 至少7天
                break
        else:
            ma_window = 30  # 默认为30天

        print(f"自动确定的移动平均窗口大小: {ma_window}")

    # 2. 使用移动平均提取趋势
    trend = time_series.rolling(window=ma_window, center=True).mean()
    # 处理边缘缺失值
    trend = trend.fillna(method='bfill').fillna(method='ffill')

    # 3. 分离周期信号
    residual = time_series - trend

    # 4. 使用FFT识别主要周期
    # 去除均值
    signal_detrended = residual - residual.mean()

    # 应用FFT
    n = len(signal_detrended)
    fft_result = fftpack.fft(signal_detrended.values)

    # 计算频率和幅度
    freqs = fftpack.fftfreq(n, d=1)  # 假设日频数据
    amplitudes = np.abs(fft_result)

    # 只保留正频率部分
    positive_freq_idx = np.where(freqs > 0)
    freqs = freqs[positive_freq_idx]
    amplitudes = amplitudes[positive_freq_idx]

    # 计算周期长度
    periods = 1 / freqs

    # 创建结果DataFrame
    fft_results = pd.DataFrame({
        'frequency': freqs,
        'period': periods,
        'amplitude': amplitudes
    })

    # 按幅度排序
    fft_results = fft_results.sort_values('amplitude', ascending=False)

    # 5. 选择主要周期
    # 移除过长或过短的周期
    valid_periods = fft_results[
        (fft_results['period'] >= 2) &  # 至少2天
        (fft_results['period'] <= len(time_series) / 3)  # 不超过序列长度的1/3
        ]

    main_periods = valid_periods.head(num_periods)['period'].values
    # 取整周期长度
    main_periods_int = [int(round(p)) for p in main_periods]

    print(f"识别出的主要周期: {main_periods_int}")

    # 6. 使用MSTL（多季节STL）分解周期成分
    # 初始化结果存储
    seasonal_components = pd.DataFrame(index=residual.index)

    # 创建临时数据用于STL分解
    temp_data = residual.copy()

    # 对每个周期单独应用STL，不累积残差
    for i, period in enumerate(main_periods_int):
        # 确保周期是奇数且大于等于3
        if period < 3:
            print(f"警告: 周期 {period} 小于最小要求值3，跳过该周期")
            continue

        # 确保周期是奇数
        if period % 2 == 0:
            period += 1
            print(f"将周期从 {main_periods_int[i]} 调整为 {period} (必须为奇数)")

        # 应用STL - 直接对原始残差进行分解，而不是对前一次分解的残差
        try:
            stl = STL(temp_data,
                      seasonal=period,
                      seasonal_deg=0,
                      trend=None)

            result = stl.fit()

            # 保存季节性成分
            seasonal_components[f'seasonal_{period}'] = result.seasonal
        except Exception as e:
            print(f"处理周期 {period} 时出错: {e}")
            continue

    # 计算最终残差 - 原始残差减去所有季节性成分的和
    remainder = residual.copy()
    for col in seasonal_components.columns:
        remainder -= seasonal_components[col]

    # 7. 整合结果
    results = {
        'original': time_series,
        'trend': trend,
        'residual': residual,
        'seasonal_components': seasonal_components,
        'remainder': remainder,
        'fft_results': fft_results,
        'main_periods': main_periods_int
    }

    return results


def analyze_real_data(file_path, date_column, value_column, ma_window=None, num_periods=3):
    """
    分析真实的时间序列数据

    参数:
    file_path: str - CSV文件路径
    date_column: str - 日期列名
    value_column: str - 值列名
    ma_window: int - 移动平均窗口大小
    num_periods: int - 要保留的主要周期数量
    """
    # 读取数据
    df = pd.read_csv(file_path)

    # 转换日期列为日期类型
    df[date_column] = pd.to_datetime(df[date_column])

    # 将数据转换为时间序列
    time_series = df.set_index(date_column)[value_column]

    # 确保数据是按日期排序的
    time_series = time_series.sort_index()

    # 应用分解方法
    results = decompose_time_series(time_series, ma_window=ma_window, num_periods=num_periods)

    # 获取周期组件数量
    n_components = len(results['seasonal_components'].columns)

    # 创建图形1：主要时间序列（原始+趋势+残差）
    fig1 = plt.figure(figsize=(14, 8))

    # 原始序列
    ax_main = fig1.add_subplot(111, label='main_series')
    ax_main.plot(results['original'], label='原始时间序列')
    ax_main.plot(results['trend'], label='趋势成分 (移动平均)')
    ax_main.plot(results['residual'], label='周期信号 (残差)')
    ax_main.set_title('时间序列分解 - 主要成分')
    ax_main.legend(loc='best')
    ax_main.grid(True)

    fig1.tight_layout()
    fig1.savefig('real_data_main_components.png')

    # 创建图形2：周期成分和残差
    n_rows = n_components + 1  # 所有季节性成分 + 剩余项
    fig2 = plt.figure(figsize=(14, 3 * n_rows))

    # 各个季节性成分
    for i, col in enumerate(results['seasonal_components'].columns):
        position = i + 1
        ax_season = fig2.add_subplot(n_rows, 1, position, label=f'seasonal_{i}')
        ax_season.plot(results['seasonal_components'][col])
        period = int(col.split('_')[-1])
        ax_season.set_title(f'周期成分 (周期 = {period} 天)', fontdict={'horizontalalignment': 'center'})
        ax_season.grid(True)

    # 最终剩余项
    remainder_pos = n_rows  # 最后一行
    ax_remainder = fig2.add_subplot(n_rows, 1, remainder_pos, label='remainder')
    ax_remainder.plot(results['remainder'])
    ax_remainder.set_title('剩余成分 (噪声)')
    ax_remainder.grid(True)

    fig2.tight_layout()
    fig2.savefig('real_data_seasonal_components.png')

    # 创建图形3：FFT频谱
    fig3 = plt.figure(figsize=(12, 6))
    ax_fft = fig3.add_subplot(111, label='fft')
    ax_fft.stem(results['fft_results']['period'][:100],
                results['fft_results']['amplitude'][:100],
                use_line_collection=True)
    ax_fft.set_xscale('log')
    ax_fft.set_xlabel('周期 (天)')
    ax_fft.set_ylabel('幅度')
    ax_fft.set_title('FFT频谱分析 - 主要周期')
    ax_fft.grid(True)

    # 在频谱图上标记主要周期
    for period in results['main_periods']:
        ax_fft.axvline(x=period, color='r', linestyle='--', alpha=0.5)

    fig3.tight_layout()
    fig3.savefig('real_data_spectrum.png')

    print(
        "图形已保存到 'real_data_main_components.png', 'real_data_seasonal_components.png' 和 'real_data_spectrum.png'")

    # 清空图形
    plt.close('all')

    return results


# 澳大利亚每日最低温度数据分析函数
def analyze_australian_temp():
    """
    分析澳大利亚每日最低温度数据
    """
    # 数据来源：澳大利亚气象局收集的墨尔本每日最低温度数据
    # 为简化，这里使用内置生成的数据，可以替换为实际从CSV文件加载的数据

    # 生成示例数据 - 模拟墨尔本1981-1990每日最低温度
    np.random.seed(8)

    # 创建日期范围（约10年）
    dates = pd.date_range(start='1981-01-01', end='1990-12-31')
    n_days = len(dates)

    # 基本温度（摄氏度）
    base_temp = 15

    # 年度温度变化 - 冬季（七月）较冷，夏季（一月）较热
    # 澳大利亚位于南半球，季节与北半球相反
    days = np.arange(n_days)
    yearly_cycle = -7 * np.cos(2 * np.pi * days / 365.25)

    # 周变化
    weekly_cycle = 0.5 * np.sin(2 * np.pi * days / 7)

    # 长期趋势（轻微变暖）
    trend = 0.001 * days

    # 随机波动
    noise = np.random.normal(0, 2, n_days)

    # 合成温度数据
    temp = base_temp + yearly_cycle + weekly_cycle + trend + noise

    # 创建时间序列
    aus_temp = pd.Series(temp, index=dates)

    # 应用时间序列分解
    print("正在分析澳大利亚墨尔本每日最低温度数据...")
    results = decompose_time_series(aus_temp, ma_window=365, num_periods=3)  # 使用一年作为移动平均窗口

    # 绘图保存
    n_components = len(results['seasonal_components'].columns)

    if n_components == 0:
        print("警告: 未检测到有效周期成分，无法生成周期分解图")
        return results

    # 创建图形1：主要时间序列（原始+趋势+残差）
    fig1 = plt.figure(figsize=(14, 8))

    # 原始序列
    ax_main = fig1.add_subplot(111, label='main_series')
    ax_main.plot(results['original'], label='原始温度数据')
    ax_main.plot(results['trend'], label='趋势成分 (移动平均)')
    ax_main.plot(results['residual'], label='周期信号 (残差)')
    ax_main.set_title('澳大利亚墨尔本每日最低温度分解 - 主要成分')
    ax_main.set_ylabel('温度 (°C)')
    ax_main.legend(loc='best')
    ax_main.grid(True)

    fig1.tight_layout()
    fig1.savefig('australia_temp_main_components.png')

    # 创建图形2：周期成分和残差
    n_rows = n_components + 1  # 所有季节性成分 + 剩余项
    fig2 = plt.figure(figsize=(14, 3 * n_rows))

    # 各个季节性成分
    for i, col in enumerate(results['seasonal_components'].columns):
        position = i + 1
        ax_season = fig2.add_subplot(n_rows, 1, position, label=f'seasonal_{i}')
        ax_season.plot(results['seasonal_components'][col])
        period = int(col.split('_')[-1])
        ax_season.set_title(f'周期成分 (周期 = {period} 天)', fontdict={'horizontalalignment': 'center'})
        ax_season.set_ylabel('温度 (°C)')
        ax_season.grid(True)

    # 最终剩余项
    remainder_pos = n_rows  # 最后一行
    ax_remainder = fig2.add_subplot(n_rows, 1, remainder_pos, label='remainder')
    ax_remainder.plot(results['remainder'])
    ax_remainder.set_title('剩余成分 (噪声)')
    ax_remainder.set_ylabel('温度 (°C)')
    ax_remainder.grid(True)

    fig2.tight_layout()
    fig2.savefig('australia_temp_seasonal_components.png')

    # 创建图形3：FFT频谱
    fig3 = plt.figure(figsize=(12, 6))
    ax_fft = fig3.add_subplot(111, label='fft')
    ax_fft.stem(results['fft_results']['period'][:100],
                results['fft_results']['amplitude'][:100],
                use_line_collection=True)
    ax_fft.set_xscale('log')
    ax_fft.set_xlabel('周期 (天)')
    ax_fft.set_ylabel('幅度')
    ax_fft.set_title('澳大利亚墨尔本每日最低温度的FFT频谱分析')
    ax_fft.grid(True)

    # 在频谱图上标记主要周期
    for period in results['main_periods']:
        ax_fft.axvline(x=period, color='r', linestyle='--', alpha=0.5)

    fig3.tight_layout()
    fig3.savefig('australia_temp_spectrum.png')

    print(
        "澳大利亚温度分析图形已保存到 'australia_temp_main_components.png', 'australia_temp_seasonal_components.png' 和 'australia_temp_spectrum.png'")

    # 清空图形
    plt.close('all')

    return results


# 示例使用
if __name__ == "__main__":
    # 首先，创建一个合成的时间序列进行演示
    np.random.seed(42)
    dates = pd.date_range(start='2020-01-01', periods=1000, freq='D')

    # 创建包含多个周期的复合时间序列
    trend = np.linspace(0, 50, 1000)  # 线性增长趋势
    seasonal_365 = 10 * np.sin(np.linspace(0, 2 * np.pi, 1000) * (1000 / 365))  # 年度周期
    seasonal_90 = 5 * np.sin(np.linspace(0, 2 * np.pi, 1000) * (1000 / 90))  # 季度周期
    seasonal_7 = 3 * np.sin(np.linspace(0, 2 * np.pi, 1000) * (1000 / 7))  # 周周期
    noise = np.random.normal(0, 1, 1000)  # 随机噪声

    time_series = pd.Series(
        trend + seasonal_365 + seasonal_90 + seasonal_7 + noise,
        index=dates
    )

    # 分析演示时间序列
    print("\n分析合成时间序列...")

    # 分析合成时间序列
    results = decompose_time_series(time_series, ma_window=40, num_periods=3)

    # 获取周期组件数量
    n_components = len(results['seasonal_components'].columns)

    # 创建图形1：主要时间序列（原始+趋势+残差）
    fig1 = plt.figure(figsize=(14, 8))

    # 原始序列
    ax_main = fig1.add_subplot(111, label='main_series')
    ax_main.plot(results['original'], label='原始时间序列')
    ax_main.plot(results['trend'], label='趋势成分 (移动平均)')
    ax_main.plot(results['residual'], label='周期信号 (残差)')
    ax_main.set_title('合成时间序列分解 - 主要成分')
    ax_main.legend(loc='best')
    ax_main.grid(True)

    fig1.tight_layout()
    fig1.savefig('time_series_main_components.png')

    # 创建图形2：周期成分和残差
    n_rows = n_components + 1  # 所有季节性成分 + 剩余项
    fig2 = plt.figure(figsize=(14, 3 * n_rows))

    # 各个季节性成分
    for i, col in enumerate(results['seasonal_components'].columns):
        position = i + 1
        ax_season = fig2.add_subplot(n_rows, 1, position, label=f'seasonal_{i}')
        ax_season.plot(results['seasonal_components'][col])
        period = int(col.split('_')[-1])
        ax_season.set_title(f'周期成分 (周期 = {period} 天)', fontdict={'horizontalalignment': 'center'})
        ax_season.grid(True)

    # 最终剩余项
    remainder_pos = n_rows  # 最后一行
    ax_remainder = fig2.add_subplot(n_rows, 1, remainder_pos, label='remainder')
    ax_remainder.plot(results['remainder'])
    ax_remainder.set_title('剩余成分 (噪声)')
    ax_remainder.grid(True)

    fig2.tight_layout()
    fig2.savefig('time_series_seasonal_components.png')

    # 创建图形3：FFT频谱
    fig3 = plt.figure(figsize=(12, 6))
    ax_fft = fig3.add_subplot(111, label='fft')
    ax_fft.stem(results['fft_results']['period'][:100],
                results['fft_results']['amplitude'][:100],
                use_line_collection=True)
    ax_fft.set_xscale('log')
    ax_fft.set_xlabel('周期 (天)')
    ax_fft.set_ylabel('幅度')
    ax_fft.set_title('合成时间序列的FFT频谱分析')
    ax_fft.grid(True)

    # 在频谱图上标记主要周期
    for period in results['main_periods']:
        ax_fft.axvline(x=period, color='r', linestyle='--', alpha=0.5)

    fig3.tight_layout()
    fig3.savefig('time_series_spectrum.png')

    print(
        "合成时间序列分析图形已保存到 'time_series_main_components.png', 'time_series_seasonal_components.png' 和 'time_series_spectrum.png'")

    # 清空图形
    plt.close('all')

    # 分析澳大利亚温度数据
    print("\n开始分析澳大利亚温度数据...")
    australia_results = analyze_australian_temp()