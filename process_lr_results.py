import re
import pandas as pd
import os
from datetime import datetime

def process_lr_results(input_file):
    """
    处理学习率搜索结果文件，按数据集分组，然后在每个数据集内按pred_len分组并排序
    
    Args:
        input_file: 输入文件路径
    """
    # 存储结果的列表
    results = []
    
    # 读取输入文件
    with open(input_file, 'r') as f:
        lines = f.readlines()
    
    # 解析结果行
    for line in lines:
        # 仅处理包含"New best for"的行
        if "New best for" in line:
            # 使用正则表达式提取数据
            match = re.search(r'New best for (\w+) pred_len=(\d+): lr=([0-9.]+), MSE=([0-9.]+), MAE=([0-9.]+)', line)
            if match:
                dataset = match.group(1)
                pred_len = int(match.group(2))
                lr = float(match.group(3))
                mse = float(match.group(4))
                mae = float(match.group(5))
                
                # 添加到结果列表
                results.append({
                    'dataset': dataset,
                    'pred_len': pred_len,
                    'lr': lr,
                    'MSE': mse,
                    'MAE': mae
                })
    
    # 转换为DataFrame
    df = pd.DataFrame(results)
    
    if df.empty:
        print("警告：未能从输入文件中提取有效数据！请检查文件格式。")
        return
    
    # 创建结果目录
    result_dir = "lr_search_results"
    if not os.path.exists(result_dir):
        os.makedirs(result_dir)
    
    # 获取当前时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 创建汇总文件
    summary_file = f"{result_dir}/all_datasets_summary_{timestamp}.txt"
    with open(summary_file, 'w') as f_summary:
        f_summary.write(f"Learning Rate Search Results Summary ({timestamp})\n")
        f_summary.write("==========================================\n\n")
    
    # 按数据集分组处理
    for dataset_name, dataset_group in df.groupby('dataset'):
        # 生成输出文件名
        output_file = f"{result_dir}/{dataset_name}_lr_search_{timestamp}.txt"
        
        # 写入输出文件
        with open(output_file, 'w') as f:
            f.write(f"Learning Rate Search Results for {dataset_name} (Generated on {timestamp})\n")
            f.write("==============================================================\n\n")
            
            # 按pred_len分组，并在每组内按lr排序
            for pred_len, group in dataset_group.groupby('pred_len'):
                f.write(f"--- Prediction Length: {pred_len} ---\n")
                
                # 在组内按lr排序
                sorted_group = group.sort_values('lr')
                
                # 为每个预测长度找到MSE最小的行
                min_mse_idx = sorted_group['MSE'].idxmin()
                min_mse_row = sorted_group.loc[min_mse_idx]
                
                # 输出排序后的结果
                for _, row in sorted_group.iterrows():
                    lr_str = f"{row['lr']:.6f}"
                    mse_str = f"{row['MSE']:.6f}"
                    mae_str = f"{row['MAE']:.6f}"
                    
                    # 标记MSE最小的行
                    marker = " (Best MSE)" if row.equals(min_mse_row) else ""
                    
                    f.write(f"lr={lr_str}, MSE={mse_str}, MAE={mae_str}{marker}\n")
                
                # 输出最佳结果摘要
                f.write(f"\nBest result for pred_len={pred_len}: lr={min_mse_row['lr']:.6f}, MSE={min_mse_row['MSE']:.6f}, MAE={min_mse_row['MAE']:.6f}\n\n")
                f.write("-" * 50 + "\n\n")
                
                # 同时添加到汇总文件
                with open(summary_file, 'a') as f_summary:
                    f_summary.write(f"Dataset: {dataset_name}, Pred_len: {pred_len}\n")
                    f_summary.write(f"  Best Learning Rate: {min_mse_row['lr']:.6f}\n")
                    f_summary.write(f"  MSE: {min_mse_row['MSE']:.6f}, MAE: {min_mse_row['MAE']:.6f}\n\n")
        
        print(f"结果已处理并写入: {output_file}")
    
    print(f"汇总报告已写入: {summary_file}")

if __name__ == "__main__":
    input_file = "learning_rate_search_results.txt"
    process_lr_results(input_file) 