You are a professional LaTeX engineer specializing in academic table formatting. I will provide you with:

1. An existing LaTeX table (baseline table)
2. Experimental results for three 2024 methods (TimeMixer, CycleNet, SOFTS) in TXT file format

Your task is to:

**Table Structure Modifications:**
- Insert the three new methods (TimeMixer, CycleNet, SOFTS) as new columns
- Position these columns chronologically between 2024 and 2025 methods in the table
- Maintain the original table's column ordering logic (typically chronological by publication year)

**Data Integration Requirements:**
- Add the experimental results for all three methods to their respective columns
- Preserve ALL existing numerical values in the original table without any modifications
- Ensure no transcription errors when copying numbers from the TXT files

**Performance Annotation Updates:**
- Recalculate and update the best (1st) and second-best (2nd) performance markings across all methods (including the new ones)
- Update the count of "1st place" achievements shown in the table statistics
- Use consistent formatting for performance highlights (typically bold for best, underlined for second-best)

**Quality Assurance:**
- Double-check all numerical values for accuracy
- Maintain consistent LaTeX formatting and syntax
- Ensure proper column alignment and spacing
- Verify that table compilation will work without errors

**Deliverable:**
Provide the complete updated LaTeX table code that is ready to compile, with all modifications clearly integrated.

Please confirm you understand these requirements before I share the baseline table and experimental results files.
latex table：

\begin{table*}[ht]
  \scriptsize
  \centering
  \caption{Full long-term forecasting results with unified lookback $L = 96$ for all other datasets.
  The best model is \textbf{boldface} and the second best is \underline{underlined}.}
  \setlength\tabcolsep{2pt}
  % c (Dataset) | c (PredLen) | 9 models * 2 metrics (MSE, MAE) = 18 data columns
  \begin{tabular}{c|c|cc|cc|cc|cc|cc|cc|cc|cc|cc}
    \hline
    \multicolumn{2}{c}{Method}     & % Spans Dataset and PredLen columns in the header context
    \multicolumn{2}{|c}{\shortstack{MDMLP-EIA \\ \textbf{Ours}}}   & \multicolumn{2}{|c}{\shortstack{xPatch \\ (2025)}} & 
    \multicolumn{2}{|c}{\shortstack{Amplifier \\ (2025)}} & \multicolumn{2}{|c}{\shortstack{iTransformer \\ (2024)}} & 
    \multicolumn{2}{|c}{\shortstack{FilterNet \\ (2024)}}  & \multicolumn{2}{|c}{\shortstack{FITS \\ (2024)}} & 
    \multicolumn{2}{|c}{\shortstack{SparseTSF \\ (2024)}}& \multicolumn{2}{|c}{\shortstack{FreTS \\ (2023)}} & 
    \multicolumn{2}{|c}{\shortstack{Dlinear \\ (2023)}} \\
    \hline
    \multicolumn{2}{c|}{Metric} % Spans Dataset and PredLen columns in the header context
    & MSE & MAE   & MSE   & MAE & MSE & MAE   & MSE   & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE \\
    \hline
    % ETTh1 Data
    \multirow{4}{*}{\rotatebox{90}{ETTh1}}
    & 96  & \textbf{0.374} & \textbf{0.383} & 0.378 & 0.390 & 0.384 & 0.387 & 0.385 & 0.397 & \underline{0.378} & 0.389 & 0.412 & 0.416 & 0.391 & 0.388 & 0.397 & 0.404 & 0.380 & \underline{0.386} \\
    & 192 & \textbf{0.429} & \textbf{0.415} & \underline{0.434} & 0.421 & 0.435 & 0.418 & 0.438 & 0.429 & 0.442 & 0.423 & 0.493 & 0.463 & 0.440 & \underline{0.418} & 0.444 & 0.429 & 0.443 & 0.434 \\
    & 336 & \textbf{0.473} & \underline{0.439} & 0.479 & 0.443 & 0.482 & 0.441 & 0.483 & 0.453 & 0.490 & 0.446 & 0.493 & 0.463 & 0.482 & \textbf{0.438} & 0.487 & 0.453 & \underline{0.475} & 0.446 \\
    & 720 & \textbf{0.471} & \textbf{0.460} & \underline{0.479} & 0.463 & 0.486 & \underline{0.460} & 0.502 & 0.486 & 0.492 & 0.463 & 0.534 & 0.513 & 0.489 & 0.460 & 0.557 & 0.537 & 0.492 & 0.488 \\
    \hline
    % ETTh2 Data
    \multirow{4}{*}{\rotatebox{90}{ETTh2}}
    & 96  & \textbf{0.275} & \textbf{0.325} & 0.288 & 0.334 & 0.287 & 0.332 & 0.295 & 0.341 & \underline{0.280} & \underline{0.328} & 0.298 & 0.345 & 0.306 & 0.343 & 0.352 & 0.395 & 0.299 & 0.349 \\
    & 192 & \textbf{0.356} & \textbf{0.376} & 0.363 & 0.383 & \underline{0.358} & \underline{0.380} & 0.378 & 0.393 & 0.362 & 0.381 & 0.388 & 0.400 & 0.383 & 0.390 & 0.411 & 0.428 & 0.386 & 0.397 \\
    & 336 & \textbf{0.405} & \textbf{0.416} & 0.414 & 0.420 & \underline{0.409} & \underline{0.417} & 0.426 & 0.430 & 0.412 & 0.421 & 0.423 & 0.430 & 0.425 & 0.424 & 0.483 & 0.476 & 0.439 & 0.442 \\
    & 720 & \textbf{0.411} & \textbf{0.430} & 0.428 & 0.442 & 0.427 & 0.438 & 0.424 & 0.440 & \underline{0.422} & \underline{0.437} & 0.432 & 0.447 & 0.426 & 0.437 & 0.605 & 0.549 & 0.581 & 0.531 \\
    \hline
    % ETTm1 Data
    \multirow{4}{*}{\rotatebox{90}{ETTm1}}
    & 96  & \textbf{0.305} & \textbf{0.335} & 0.316 & 0.342 & \underline{0.309} & \underline{0.337} & 0.328 & 0.356 & 0.313 & 0.341 & 0.338 & 0.354 & 0.339 & 0.356 & 0.341 & 0.363 & 0.331 & 0.351 \\
    & 192 & \textbf{0.359} & \textbf{0.362} & 0.367 & 0.369 & \underline{0.364} & \underline{0.365} & 0.380 & 0.382 & 0.369 & 0.368 & 0.385 & 0.377 & 0.387 & 0.379 & 0.386 & 0.387 & 0.377 & 0.373 \\
    & 336 & \textbf{0.391} & \textbf{0.386} & \underline{0.395} & 0.391 & 0.396 & \underline{0.388} & 0.416 & 0.408 & 0.399 & 0.391 & 0.418 & 0.398 & 0.418 & 0.400 & 0.415 & 0.407 & 0.406 & 0.396 \\
    & 720 & \textbf{0.459} & \textbf{0.424} & \underline{0.464} & 0.431 & 0.471 & 0.431 & 0.484 & 0.446 & 0.466 & \underline{0.429} & 0.485 & 0.436 & 0.488 & 0.437 & 0.481 & 0.448 & 0.469 & \underline{0.434} \\
    \hline
    % ETTm2 Data
    \multirow{4}{*}{\rotatebox{90}{ETTm2}}
    & 96  & \textbf{0.168} & \textbf{0.247} & 0.174 & 0.253 & 0.173 & 0.252 & 0.177 & 0.255 & \underline{0.171} & \underline{0.250} & 0.183 & 0.259 & 0.180 & 0.258 & 0.184 & 0.272 & 0.183 & 0.258 \\
    & 192 & \textbf{0.233} & \textbf{0.292} & 0.241 & 0.297 & 0.239 & 0.295 & 0.246 & 0.301 & \underline{0.235} & \underline{0.293} & 0.248 & 0.300 & 0.244 & 0.298 & 0.251 & 0.318 & 0.247 & 0.301 \\
    & 336 & \textbf{0.292} & \textbf{0.329} & 0.300 & 0.336 & 0.299 & 0.334 & 0.310 & 0.343 & \underline{0.294} & \underline{0.330} & 0.308 & 0.350 & 0.303 & 0.335 & 0.309 & 0.354 & 0.308 & 0.337 \\
    & 720 & \underline{0.391} & \textbf{0.388} & 0.401 & 0.393 & \textbf{0.391} & \underline{0.389} & 0.411 & 0.400 & 0.393 & 0.389 & 0.409 & 0.417 & 0.400 & 0.392 & 0.417 & 0.420 & 0.410 & 0.394 \\
    \hline
    % electricity Data
    \multirow{4}{*}{\rotatebox{90}{electricity}}
    & 96  & \textbf{0.140} & \underline{0.232} & 0.162 & 0.246 & 0.147 & 0.237 & \underline{0.145} & \textbf{0.231} & 0.183 & 0.259 & 0.206 & 0.281 & 0.201 & 0.263 & 0.171 & 0.251 & 0.199 & 0.271 \\
    & 192 & \textbf{0.157} & \textbf{0.247} & 0.170 & 0.253 & \underline{0.162} & \underline{0.249} & 0.166 & 0.250 & 0.189 & 0.267 & 0.203 & 0.281 & 0.199 & 0.266 & 0.181 & 0.262 & 0.198 & 0.274 \\
    & 336 & \textbf{0.174} & \underline{0.264} & 0.185 & 0.268 & \underline{0.176} & \textbf{0.264} & 0.183 & 0.270 & 0.205 & 0.284 & 0.220 & 0.302 & 0.212 & 0.281 & 0.198 & 0.280 & 0.210 & 0.289 \\
    & 720 & \textbf{0.198} & \textbf{0.285} & 0.224 & 0.302 & \underline{0.209} & \underline{0.292} & 0.221 & 0.303 & 0.246 & 0.317 & 0.275 & 0.354 & 0.252 & 0.314 & 0.241 & 0.318 & 0.245 & 0.320 \\
    \hline
    % exchange Data
    \multirow{4}{*}{\rotatebox{90}{Exchange}}
    & 96  & \underline{0.083} & \underline{0.201} & 0.083 & \textbf{0.199} & 0.084 & 0.203 & 0.086 & 0.205 & 0.087 & 0.206 & 0.147 & 0.277 & 0.151 & 0.296 & \textbf{0.082} & 0.202 & 0.096 & 0.214 \\
    & 192 & 0.176 & 0.298 & 0.177 & 0.298 & 0.177 & 0.299 & 0.179 & 0.301 & 0.236 & 0.352 & 0.255 & 0.370 & 0.243 & 0.364 & \textbf{0.155} & \textbf{0.288} & \underline{0.166} & \underline{0.289} \\
    & 336 & 0.335 & 0.418 & 0.347 & 0.425 & 0.339 & 0.420 & 0.348 & 0.426 & 0.384 & 0.455 & 0.416 & 0.477 & 0.382 & 0.458 & \underline{0.284} & \underline{0.396} & \textbf{0.246} & \textbf{0.368} \\
    & 720 & \textbf{0.853} & \textbf{0.698} & \underline{0.877} & \underline{0.706} & 0.930 & 0.734 & 0.895 & 0.717 & 0.933 & 0.736 & 0.989 & 0.758 & 0.958 & 0.750 & 1.301 & 0.811 & 0.940 & 0.707 \\
    \hline
    % solar Data
    \multirow{4}{*}{\rotatebox{90}{Solar}}
    & 96  & 0.211 & \underline{0.218} & \underline{0.203} & 0.218 & 0.235 & 0.239 & \textbf{0.200} & \textbf{0.212} & 0.228 & 0.238 & 0.374 & 0.338 & 0.267 & 0.264 & 0.255 & 0.264 & 0.286 & 0.300 \\
    & 192 & \textbf{0.232} & \textbf{0.236} & 0.242 & 0.240 & 0.274 & 0.259 & \underline{0.233} & \underline{0.238} & 0.274 & 0.263 & 0.422 & 0.365 & 0.301 & 0.283 & 0.260 & 0.262 & 0.317 & 0.318 \\
    & 336 & \underline{0.255} & \textbf{0.246} & 0.258 & \underline{0.248} & 0.309 & 0.277 & \textbf{0.253} & 0.253 & 0.313 & 0.284 & 0.462 & 0.377 & 0.326 & 0.295 & 0.287 & 0.277 & 0.363 & 0.332 \\
    & 720 & \textbf{0.257} & \textbf{0.250} & 0.273 & 0.257 & 0.308 & 0.275 & \underline{0.260} & \underline{0.256} & 0.306 & 0.279 & 0.453 & 0.363 & 0.320 & 0.289 & 0.307 & 0.282 & 0.374 & 0.325 \\
    \hline
    % traffic Data
    \multirow{4}{*}{\rotatebox{90}{Traffic}}
    & 96  & 0.454 & \underline{0.270} & 0.475 & 0.280 & 0.490 & 0.296 & \textbf{0.393} & \textbf{0.235} & \underline{0.433} & 0.278 & 0.674 & 0.397 & 0.597 & 0.329 & 0.575 & 0.328 & 0.678 & 0.365 \\
    & 192 & 0.467 & 0.278 & 0.487 & \underline{0.277} & 0.491 & 0.293 & \textbf{0.410} & \textbf{0.246} & \underline{0.448} & 0.283 & 0.626 & 0.380 & 0.567 & 0.312 & 0.561 & 0.320 & 0.634 & 0.342 \\
    & 336 & 0.489 & 0.292 & 0.499 & \underline{0.280} & 0.502 & 0.297 & \textbf{0.412} & \textbf{0.247} & \underline{0.463} & 0.289 & 0.679 & 0.436 & 0.577 & 0.315 & 0.571 & 0.326 & 0.637 & 0.344 \\
    & 720 & 0.517 & 0.311 & 0.538 & \underline{0.295} & 0.534 & 0.314 & \textbf{0.444} & \textbf{0.264} & \underline{0.492} & 0.305 & 0.788 & 0.496 & 0.615 & 0.335 & 0.610 & 0.344 & 0.667 & 0.364 \\
    \hline
    % weather Data
    \multirow{4}{*}{\rotatebox{90}{Weather}}
    & 96  & \underline{0.156} & \underline{0.195} & 0.166 & 0.202 & 0.166 & 0.204 & 0.171 & 0.206 & \textbf{0.155} & \textbf{0.193} & 0.173 & 0.213 & 0.182 & 0.217 & 0.174 & 0.212 & 0.208 & 0.233 \\
    & 192 & \textbf{0.203} & \textbf{0.238} & 0.211 & 0.243 & 0.211 & 0.245 & 0.221 & 0.249 & \underline{0.204} & \underline{0.241} & 0.221 & 0.255 & 0.227 & 0.256 & 0.213 & 0.249 & 0.244 & 0.268 \\
    & 336 & \textbf{0.261} & \textbf{0.283} & 0.267 & \underline{0.284} & 0.266 & 0.284 & 0.278 & 0.292 & 0.265 & 0.285 & 0.279 & 0.295 & 0.281 & 0.295 & \underline{0.263} & 0.294 & 0.288 & 0.306 \\
    & 720 & \underline{0.339} & \textbf{0.333} & 0.345 & 0.336 & 0.344 & \underline{0.335} & 0.355 & 0.343 & 0.354 & 0.342 & 0.359 & 0.345 & 0.356 & 0.344 & \textbf{0.338} & 0.352 & 0.347 & 0.362 \\
    \hline
    \multicolumn{2}{c|}{count of $1^{st}$} 
    & 27 & 24 & 0 & 1 & 1 & 2 & 6 & 5 & 1 & 1 & 0 & 0 & 0 & 1 & 2 & 1 & 0 & 1 \\
    \hline
  \end{tabular}
    \label{tab:full-experiments}
\end{table*}