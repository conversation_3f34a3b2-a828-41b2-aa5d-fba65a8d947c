import os
import re
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import glob
from matplotlib.ticker import ScalarFormatter

# 设置结果目录和保存目录
RESULT_FILES = [
    "xpatch_test_results.txt",
    "dlinear_test_results.txt",
    "mymodel_test_results.txt",
    "FITS_test_results.txt", 
    "itransformer_test_results.txt",
    "Amplifier_test_results.txt"
]

MODEL_NAMES = {
    "xpatch": "xPatch",
    "dlinear": "DLinear",
    "mymodel": "MyModel",
    "FITS": "FITS",
    "itransformer": "iTransformer",
    "Amplifier": "Amplifier"
}

DATASET_NAMES = ["ETTh1", "ETTh2", "ETTm1", "ETTm2", "electricity", "exchange", "traffic", "weather", "solar"]
PRED_LENGTHS = [96, 192, 336, 720]
METRICS = ["MSE", "MAE"]

COLORS = {
    "xPatch": "blue",
    "DLinear": "green",
    "MyModel": "red",
    "FITS": "purple",
    "iTransformer": "orange",
    "Amplifier": "brown"
}

MARKERS = {
    "xPatch": "o",
    "DLinear": "s",
    "MyModel": "^",
    "FITS": "d",
    "iTransformer": "p",
    "Amplifier": "*"
}

OUTPUT_DIR = "performance_plots"
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

# 解析结果文件
def parse_result_file(file_path):
    results = []
    
    if not os.path.exists(file_path):
        print(f"Warning: File {file_path} does not exist.")
        return results
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # 使用正则表达式提取模型设置和指标
    pattern = r'(.*?)_(\d+)_.*?_(\w+)(?:_.*?)?\s+\n(?:.*?)mse:(.*?), mae:(.*?)(?:\n|$)'
    matches = re.findall(pattern, content, re.MULTILINE)
    
    for match in matches:
        dataset = match[0]
        pred_len = int(match[1])
        model = match[2]
        mse = float(match[3].strip())
        mae = float(match[4].strip())
        
        results.append({
            'dataset': dataset,
            'pred_len': pred_len,
            'model': model,
            'mse': mse,
            'mae': mae
        })
    
    return results

# 收集所有结果
all_results = []
for result_file in RESULT_FILES:
    model_name = result_file.split('_')[0].lower()
    results = parse_result_file(result_file)
    for result in results:
        result['model_name'] = MODEL_NAMES.get(model_name, model_name)
        all_results.append(result)

# 转换为DataFrame便于分析
df = pd.DataFrame(all_results)

# 确认我们有数据
if df.empty:
    print("Error: No data was loaded from result files.")
    exit(1)

print(f"Loaded {len(df)} results entries.")

# 绘制各数据集不同预测长度下各模型的性能对比图
def plot_performance_comparison():
    for dataset in DATASET_NAMES:
        dataset_data = df[df['dataset'] == dataset]
        
        if dataset_data.empty:
            print(f"No data for dataset {dataset}, skipping.")
            continue
            
        for metric in METRICS:
            metric_lower = metric.lower()
            
            plt.figure(figsize=(12, 8))
            
            for model in MODEL_NAMES.values():
                model_data = dataset_data[dataset_data['model_name'] == model]
                
                if model_data.empty:
                    print(f"No data for model {model} on dataset {dataset}, skipping.")
                    continue
                
                # 按预测长度排序
                model_data = model_data.sort_values('pred_len')
                
                plt.plot(
                    model_data['pred_len'],
                    model_data[metric_lower],
                    label=model,
                    marker=MARKERS[model],
                    color=COLORS[model],
                    linewidth=2,
                    markersize=8
                )
            
            plt.title(f'{dataset} Dataset: {metric} vs Prediction Length', fontsize=16)
            plt.xlabel('Prediction Length', fontsize=14)
            plt.ylabel(metric, fontsize=14)
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.legend(fontsize=12)
            
            # 使用对数刻度更好地显示数据差异
            if metric == "MSE":
                plt.yscale('log')
                plt.gca().yaxis.set_major_formatter(ScalarFormatter())
            
            # 设置x轴刻度
            plt.xticks(PRED_LENGTHS)
            
            # 保存图像
            plt.tight_layout()
            plt.savefig(os.path.join(OUTPUT_DIR, f'{dataset}_{metric}_comparison.png'), dpi=300)
            plt.close()
            
            print(f"Generated {dataset}_{metric}_comparison.png")

# 为每个模型绘制在所有数据集上的性能热图
def plot_heatmaps():
    for model in MODEL_NAMES.values():
        model_data = df[df['model_name'] == model]
        
        if model_data.empty:
            print(f"No data for model {model}, skipping heatmap.")
            continue
            
        for metric in METRICS:
            metric_lower = metric.lower()
            
            # 创建数据透视表
            pivot = pd.pivot_table(
                model_data,
                values=metric_lower,
                index='dataset',
                columns='pred_len',
                aggfunc=np.mean
            )
            
            # 确保所有预测长度都包含在内
            for pred_len in PRED_LENGTHS:
                if pred_len not in pivot.columns:
                    pivot[pred_len] = np.nan
            
            # 按预测长度排序列
            pivot = pivot.reindex(columns=sorted(pivot.columns))
            
            plt.figure(figsize=(10, 8))
            cmap = 'YlOrRd' if metric == 'MSE' else 'YlGnBu'
            
            im = plt.imshow(pivot.values, cmap=cmap, aspect='auto')
            plt.colorbar(im, label=metric)
            
            # 设置坐标轴标签
            plt.xticks(np.arange(len(pivot.columns)), pivot.columns, fontsize=12)
            plt.yticks(np.arange(len(pivot.index)), pivot.index, fontsize=12)
            
            plt.title(f'{model} - {metric} Performance Across Datasets', fontsize=16)
            plt.xlabel('Prediction Length', fontsize=14)
            plt.ylabel('Dataset', fontsize=14)
            
            # 添加文本注释
            for i in range(len(pivot.index)):
                for j in range(len(pivot.columns)):
                    value = pivot.iloc[i, j]
                    if not np.isnan(value):
                        text_color = 'white' if value > pivot.values.mean() else 'black'
                        plt.text(j, i, f'{value:.4f}', 
                                ha='center', va='center', 
                                fontsize=10, color=text_color)
            
            plt.tight_layout()
            plt.savefig(os.path.join(OUTPUT_DIR, f'{model}_{metric}_heatmap.png'), dpi=300)
            plt.close()
            
            print(f"Generated {model}_{metric}_heatmap.png")

# 绘制所有模型在所有数据集上的平均性能
def plot_average_performance():
    # 计算每个模型在所有数据集上的平均性能
    avg_performance = df.groupby(['model_name', 'pred_len']).agg({'mse': 'mean', 'mae': 'mean'}).reset_index()
    
    for metric in METRICS:
        metric_lower = metric.lower()
        
        plt.figure(figsize=(12, 8))
        
        for model in MODEL_NAMES.values():
            model_data = avg_performance[avg_performance['model_name'] == model]
            
            if model_data.empty:
                continue
                
            model_data = model_data.sort_values('pred_len')
            
            plt.plot(
                model_data['pred_len'],
                model_data[metric_lower],
                label=model,
                marker=MARKERS[model],
                color=COLORS[model],
                linewidth=2,
                markersize=8
            )
        
        plt.title(f'Average {metric} Performance Across All Datasets', fontsize=16)
        plt.xlabel('Prediction Length', fontsize=14)
        plt.ylabel(f'Average {metric}', fontsize=14)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend(fontsize=12)
        
        # 使用对数刻度更好地显示数据差异
        if metric == "MSE":
            plt.yscale('log')
            plt.gca().yaxis.set_major_formatter(ScalarFormatter())
        
        # 设置x轴刻度
        plt.xticks(PRED_LENGTHS)
        
        plt.tight_layout()
        plt.savefig(os.path.join(OUTPUT_DIR, f'average_{metric}_performance.png'), dpi=300)
        plt.close()
        
        print(f"Generated average_{metric}_performance.png")

# 为每个预测长度绘制每个模型的排名
def plot_model_rankings():
    for pred_len in PRED_LENGTHS:
        pred_len_data = df[df['pred_len'] == pred_len]
        
        if pred_len_data.empty:
            print(f"No data for prediction length {pred_len}, skipping ranking.")
            continue
        
        for metric in METRICS:
            metric_lower = metric.lower()
            
            # 计算每个模型在每个数据集上的排名
            rankings = []
            
            for dataset in DATASET_NAMES:
                dataset_data = pred_len_data[pred_len_data['dataset'] == dataset]
                
                if dataset_data.empty:
                    continue
                
                # 根据指标排序并分配排名
                dataset_data = dataset_data.sort_values(metric_lower)
                dataset_data['rank'] = range(1, len(dataset_data) + 1)
                
                for _, row in dataset_data.iterrows():
                    rankings.append({
                        'model': row['model_name'],
                        'dataset': dataset,
                        'rank': row['rank'],
                        'value': row[metric_lower]
                    })
            
            rankings_df = pd.DataFrame(rankings)
            
            if rankings_df.empty:
                continue
                
            # 计算每个模型的平均排名
            avg_rankings = rankings_df.groupby('model').agg({'rank': 'mean'}).reset_index()
            avg_rankings = avg_rankings.sort_values('rank')
            
            plt.figure(figsize=(10, 6))
            
            bars = plt.barh(
                avg_rankings['model'],
                avg_rankings['rank'],
                color=[COLORS[model] for model in avg_rankings['model']],
                alpha=0.7
            )
            
            plt.title(f'Average {metric} Ranking for Prediction Length {pred_len}', fontsize=16)
            plt.xlabel('Average Rank (Lower is Better)', fontsize=14)
            plt.ylabel('Model', fontsize=14)
            plt.grid(True, linestyle='--', alpha=0.5, axis='x')
            
            # 添加数值标签
            for bar in bars:
                width = bar.get_width()
                plt.text(
                    width + 0.1,
                    bar.get_y() + bar.get_height()/2,
                    f'{width:.2f}',
                    va='center',
                    fontsize=10
                )
            
            plt.tight_layout()
            plt.savefig(os.path.join(OUTPUT_DIR, f'ranking_{metric}_pred{pred_len}.png'), dpi=300)
            plt.close()
            
            print(f"Generated ranking_{metric}_pred{pred_len}.png")

if __name__ == "__main__":
    print("Generating performance comparison plots...")
    plot_performance_comparison()
    
    print("\nGenerating heatmap plots...")
    plot_heatmaps()
    
    print("\nGenerating average performance plots...")
    plot_average_performance()
    
    print("\nGenerating model ranking plots...")
    plot_model_rankings()
    
    print("\nAll plots generated successfully in the 'performance_plots' directory.") 