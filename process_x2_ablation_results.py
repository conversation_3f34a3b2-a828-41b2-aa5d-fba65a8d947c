import os
import re
import pandas as pd
import numpy as np

# Define the log directory and output directory
log_dir = "logs/ablation"
output_dir = "ablation_results"

# Create output directory if it doesn't exist
os.makedirs(output_dir, exist_ok=True)

# Dictionary to store experiment results
results = {}

# Experiment variants and their respective directories
variants = {
    "No x2": "1.1 去掉x2",
    "MLP fusion x1,x2": "1.2 mlp融合x1,x2",
    "Seasonal+Trend output": "2.1 seasonal_output + trend_output",
    "MLP fusion Seasonal+Trend output": "2.2 普通注意力机制",
    "Final version": "最终版本",
}

# Datasets to process
datasets = ["ETTh1", "weather"]

# Function to extract MSE and MAE from log file
def extract_metrics(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # Find the last line with metrics
            mse_match = re.search(r'mse:([\d\.]+)', content)
            mae_match = re.search(r'mae:([\d\.]+)', content)
            
            if mse_match and mae_match:
                mse = float(mse_match.group(1))
                mae = float(mae_match.group(1))
                return mse, mae
            else:
                return None, None
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return None, None

# Collect results from log files
for variant_name, variant_dir in variants.items():
    variant_path = os.path.join(log_dir, variant_dir)
    if not os.path.exists(variant_path):
        print(f"Warning: Directory {variant_path} does not exist")
        continue
    
    results[variant_name] = {}
    
    # Process each dataset
    for dataset in datasets:
        results[variant_name][dataset] = {}
        
        # Process each prediction length
        for pred_len in [96, 192, 336, 720]:
            log_file = f"MyModel_{dataset}_96_{pred_len}.log"
            log_path = os.path.join(variant_path, log_file)
            
            if os.path.exists(log_path):
                mse, mae = extract_metrics(log_path)
                if mse is not None and mae is not None:
                    results[variant_name][dataset][pred_len] = {"MSE": mse, "MAE": mae}
                else:
                    print(f"Could not extract metrics from {log_path}")
            else:
                print(f"Warning: File {log_path} does not exist")

# Create individual DataFrames for each dataset for better visualization
dfs = {}

for dataset in datasets:
    # Initialize with empty values
    columns = pd.MultiIndex.from_product([['96', '192', '336', '720'], ['MSE', 'MAE']])
    df = pd.DataFrame(index=variants.keys(), columns=columns)
    
    # Fill the DataFrame with results
    for variant in results:
        if dataset in results[variant]:
            for pred_len in results[variant][dataset]:
                df.loc[variant, (str(pred_len), 'MSE')] = results[variant][dataset][pred_len]["MSE"]
                df.loc[variant, (str(pred_len), 'MAE')] = results[variant][dataset][pred_len]["MAE"]
    
    # Fill NaN values with "-" for better readability
    df = df.fillna("-")
    dfs[dataset] = df

# Create a consolidated DataFrame with results from all datasets
# Format: multi-level index with dataset and model variant, columns are prediction lengths and metrics
consolidated_indexes = []
for dataset in datasets:
    for variant in variants.keys():
        consolidated_indexes.append((dataset, variant))

multi_index = pd.MultiIndex.from_tuples(consolidated_indexes, names=['Dataset', 'Method'])
columns = pd.MultiIndex.from_product([['96', '192', '336', '720'], ['MSE', 'MAE']])
consolidated_df = pd.DataFrame(index=multi_index, columns=columns)

# Fill the consolidated DataFrame
for dataset in datasets:
    for variant in results:
        if dataset in results[variant]:
            for pred_len in results[variant][dataset]:
                if pred_len in results[variant][dataset]:
                    consolidated_df.loc[(dataset, variant), (str(pred_len), 'MSE')] = results[variant][dataset][pred_len]["MSE"]
                    consolidated_df.loc[(dataset, variant), (str(pred_len), 'MAE')] = results[variant][dataset][pred_len]["MAE"]

# Fill NaN values with "-" for better readability
consolidated_df = consolidated_df.fillna("-")

# Save all results
try:
    # Save consolidated results
    consolidated_csv_path = os.path.join(output_dir, "ablation_results_consolidated.csv")
    consolidated_df.to_csv(consolidated_csv_path)
    print(f"Consolidated results saved to {consolidated_csv_path}")
    
    # Create a combined Excel file with all sheets 
    with pd.ExcelWriter(os.path.join(output_dir, "ablation_results_all.xlsx")) as writer:
        # First sheet is consolidated view
        consolidated_df.to_excel(writer, sheet_name="All Datasets")
        
        # Also include individual dataset sheets
        for dataset, df in dfs.items():
            df.to_excel(writer, sheet_name=dataset)
    
    print(f"Complete results saved to {os.path.join(output_dir, 'ablation_results_all.xlsx')}")
except Exception as e:
    print(f"Could not save Excel file: {e}")

# Print consolidated results
print("\nConsolidated Ablation Experiment Results:")
print("=========================================")
print(consolidated_df)

# Calculate improvements for each dataset
for dataset in datasets:
    if "No x2" in results and "Final version" in results:
        if dataset in results["No x2"] and dataset in results["Final version"]:
            print(f"\nImprovement of Final Version over No x2 Baseline for {dataset}:")
            print("=" * (55 + len(dataset)))
            for pred_len in [96, 192, 336, 720]:
                if (pred_len in results["No x2"][dataset] and 
                    pred_len in results["Final version"][dataset]):
                    baseline_mse = results["No x2"][dataset][pred_len]["MSE"]
                    final_mse = results["Final version"][dataset][pred_len]["MSE"]
                    mse_improvement = (baseline_mse - final_mse) / baseline_mse * 100
                    
                    baseline_mae = results["No x2"][dataset][pred_len]["MAE"]
                    final_mae = results["Final version"][dataset][pred_len]["MAE"]
                    mae_improvement = (baseline_mae - final_mae) / baseline_mae * 100
                    
                    print(f"Horizon {pred_len}: MSE improved by {mse_improvement:.2f}%, MAE improved by {mae_improvement:.2f}%") 