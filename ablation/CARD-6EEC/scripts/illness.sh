if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

seq_len=36 # 36 104 148
model_name=CARD

root_path_name=../dataset/
data_path_name=national_illness.csv
model_id_name=national_illness
data_name=custom

random_seed=2023
for pred_len in 24 36 48 60
do
    python -u run_longExp.py \
      --random_seed $random_seed \
      --is_training 1 \
      --root_path $root_path_name \
      --data_path $data_path_name \
      --model_id $model_id_name'_'$seq_len'_'$pred_len \
      --model $model_name \
      --data $data_name \
      --features M \
      --seq_len $seq_len \
      --pred_len $pred_len \
      --enc_in 7 \
      --label_len 18 \
      --e_layers 2 \
      --n_heads 2 \
      --d_model 16 \
      --d_ff 32 \
      --dropout 0.3\
      --fc_dropout 0.3\
      --head_dropout 0\
      --patch_len 24\
      --stride 2\
      --des 'Exp' \
      --train_epochs 100\
      --patience 10\
      --itr 1 --batch_size 128 --learning_rate 0.0001 --merge_size 2 \
      --lradj 'CARD'\
      --warmup_epochs 0 > logs/$model_name'_'$model_id_name'_'$seq_len'_'$pred_len.log
done