<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="LanguageDetectionInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="5">
            <item index="0" class="java.lang.String" itemvalue="torch" />
            <item index="1" class="java.lang.String" itemvalue="pandas" />
            <item index="2" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="3" class="java.lang.String" itemvalue="matplotlib" />
            <item index="4" class="java.lang.String" itemvalue="numpy" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>