#!/bin/bash
# 测试结果分析脚本 - 从各模型的测试结果文件中提取和分析性能数据

echo "===========================================" 
echo "时间序列预测模型测试结果分析"
echo "===========================================" 

# 结果文件列表
RESULT_FILES=(
    "xpatch_test_results.txt"
    "dlinear_test_results.txt"
    "mymodel_test_results.txt"
    "FITS_test_results.txt"
    "itransformer_test_results.txt"
    "Amplifier_test_results.txt"
)

# 数据集名称列表
DATASETS=("ETTh1" "ETTh2" "ETTm1" "ETTm2" "electricity" "exchange" "traffic" "weather" "solar")

# 预测长度列表
PRED_LENGTHS=(96 192 336 720)

# 检查结果文件是否存在
missing_files=0
for file in "${RESULT_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "警告: 结果文件 $file 不存在"
        missing_files=$((missing_files + 1))
    fi
done

if [ $missing_files -eq ${#RESULT_FILES[@]} ]; then
    echo "错误: 所有结果文件均不存在，请先运行测试脚本"
    exit 1
fi

# 创建临时分析目录
ANALYSIS_DIR="./result_analysis"
if [ ! -d "$ANALYSIS_DIR" ]; then
    mkdir -p "$ANALYSIS_DIR"
fi

# 函数：提取特定数据集和预测长度的MSE和MAE值
extract_metrics() {
    local result_file=$1
    local dataset=$2
    local pred_len=$3
    local model=$(basename "$result_file" _test_results.txt)
    
    # 格式化模型名称显示
    case "$model" in
        "xpatch") model_display="xPatch" ;;
        "dlinear") model_display="DLinear" ;;
        "mymodel") model_display="MyModel" ;;
        "FITS") model_display="FITS" ;;
        "itransformer") model_display="iTransformer" ;;
        "Amplifier") model_display="Amplifier" ;;
        *) model_display="$model" ;;
    esac
    
    # 使用正则表达式提取MSE和MAE
    if [ -f "$result_file" ]; then
        # 提取包含该数据集和预测长度的行，并获取MSE和MAE
        local line=$(grep -E "^${dataset}_${pred_len}_" "$result_file" | head -n 1)
        if [ ! -z "$line" ]; then
            local mse=$(echo "$line" | grep -oE "mse:([0-9]+\.[0-9]+)" | cut -d':' -f2)
            local mae=$(echo "$line" | grep -oE "mae:([0-9]+\.[0-9]+)" | cut -d':' -f2)
            
            if [ ! -z "$mse" ] && [ ! -z "$mae" ]; then
                echo "$model_display,$mse,$mae"
            else
                echo "$model_display,N/A,N/A"
            fi
        else
            echo "$model_display,N/A,N/A"
        fi
    else
        echo "$model_display,N/A,N/A"
    fi
}

# 为每个数据集和预测长度组合生成CSV文件
for dataset in "${DATASETS[@]}"; do
    for pred_len in "${PRED_LENGTHS[@]}"; do
        output_file="$ANALYSIS_DIR/${dataset}_${pred_len}_comparison.csv"
        
        # 创建CSV头
        echo "Model,MSE,MAE" > "$output_file"
        
        # 提取各模型的指标并写入CSV
        for result_file in "${RESULT_FILES[@]}"; do
            metrics=$(extract_metrics "$result_file" "$dataset" "$pred_len")
            echo "$metrics" >> "$output_file"
        done
        
        echo "已生成 $dataset 数据集 预测长度 $pred_len 的比较分析"
    done
done

# 为每个预测长度生成模型排名表
for pred_len in "${PRED_LENGTHS[@]}"; do
    output_file="$ANALYSIS_DIR/ranking_pred${pred_len}.csv"
    
    # 创建CSV头
    echo "Model,Dataset,MSE,MAE,MSE_Rank,MAE_Rank" > "$output_file"
    
    # 收集所有数据集上的结果
    for dataset in "${DATASETS[@]}"; do
        for result_file in "${RESULT_FILES[@]}"; do
            model=$(basename "$result_file" _test_results.txt)
            
            # 格式化模型名称显示
            case "$model" in
                "xpatch") model_display="xPatch" ;;
                "dlinear") model_display="DLinear" ;;
                "mymodel") model_display="MyModel" ;;
                "FITS") model_display="FITS" ;;
                "itransformer") model_display="iTransformer" ;;
                "Amplifier") model_display="Amplifier" ;;
                *) model_display="$model" ;;
            esac
            
            if [ -f "$result_file" ]; then
                # 提取指标
                line=$(grep -E "^${dataset}_${pred_len}_" "$result_file" | head -n 1)
                if [ ! -z "$line" ]; then
                    mse=$(echo "$line" | grep -oE "mse:([0-9]+\.[0-9]+)" | cut -d':' -f2)
                    mae=$(echo "$line" | grep -oE "mae:([0-9]+\.[0-9]+)" | cut -d':' -f2)
                    
                    if [ ! -z "$mse" ] && [ ! -z "$mae" ]; then
                        echo "$model_display,$dataset,$mse,$mae,,," >> "$output_file"
                    fi
                fi
            fi
        done
    done
    
    echo "已生成预测长度 $pred_len 的模型性能排名表"
done

# 生成汇总报告
summary_file="$ANALYSIS_DIR/summary_report.txt"
echo "时间序列预测模型测试结果汇总分析" > "$summary_file"
echo "====================================" >> "$summary_file"
echo "" >> "$summary_file"
echo "分析报告生成时间: $(date)" >> "$summary_file"
echo "" >> "$summary_file"

# 计算各模型的平均性能
echo "各模型平均性能:" >> "$summary_file"
echo "----------------" >> "$summary_file"
echo "模型名称 | 平均MSE | 平均MAE" >> "$summary_file"
echo "-------|---------|--------" >> "$summary_file"

for result_file in "${RESULT_FILES[@]}"; do
    model=$(basename "$result_file" _test_results.txt)
    
    # 格式化模型名称显示
    case "$model" in
        "xpatch") model_display="xPatch" ;;
        "dlinear") model_display="DLinear" ;;
        "mymodel") model_display="MyModel" ;;
        "FITS") model_display="FITS" ;;
        "itransformer") model_display="iTransformer" ;;
        "Amplifier") model_display="Amplifier" ;;
        *) model_display="$model" ;;
    esac
    
    if [ -f "$result_file" ]; then
        # 提取并计算平均MSE和MAE
        mse_sum=$(grep -oE "mse:([0-9]+\.[0-9]+)" "$result_file" | cut -d':' -f2 | awk '{sum+=$1} END {print sum}')
        mae_sum=$(grep -oE "mae:([0-9]+\.[0-9]+)" "$result_file" | cut -d':' -f2 | awk '{sum+=$1} END {print sum}')
        mse_count=$(grep -oE "mse:([0-9]+\.[0-9]+)" "$result_file" | wc -l)
        mae_count=$(grep -oE "mae:([0-9]+\.[0-9]+)" "$result_file" | wc -l)
        
        if [ $mse_count -gt 0 ] && [ $mae_count -gt 0 ]; then
            avg_mse=$(awk -v sum="$mse_sum" -v count="$mse_count" 'BEGIN {printf "%.4f", sum/count}')
            avg_mae=$(awk -v sum="$mae_sum" -v count="$mae_count" 'BEGIN {printf "%.4f", sum/count}')
            echo "$model_display | $avg_mse | $avg_mae" >> "$summary_file"
        else
            echo "$model_display | N/A | N/A" >> "$summary_file"
        fi
    else
        echo "$model_display | 结果文件不存在 | 结果文件不存在" >> "$summary_file"
    fi
done

echo "" >> "$summary_file"
echo "数据集测试覆盖率:" >> "$summary_file"
echo "----------------" >> "$summary_file"

# 计算每个数据集的测试覆盖率
for dataset in "${DATASETS[@]}"; do
    total_tests=$((${#RESULT_FILES[@]} * ${#PRED_LENGTHS[@]}))
    completed_tests=0
    
    for result_file in "${RESULT_FILES[@]}"; do
        if [ -f "$result_file" ]; then
            for pred_len in "${PRED_LENGTHS[@]}"; do
                # 检查是否存在该数据集和预测长度的结果
                count=$(grep -E "^${dataset}_${pred_len}_" "$result_file" | wc -l)
                if [ $count -gt 0 ]; then
                    completed_tests=$((completed_tests + 1))
                fi
            done
        fi
    done
    
    coverage=$(awk -v comp="$completed_tests" -v tot="$total_tests" 'BEGIN {printf "%.1f%%", (comp/tot)*100}')
    echo "$dataset: $completed_tests / $total_tests ($coverage)" >> "$summary_file"
done

echo "" >> "$summary_file"
echo "建议后续行动:" >> "$summary_file"
echo "----------------" >> "$summary_file"
echo "1. 运行 python generate_summary_plots.py 生成可视化性能对比图表" >> "$summary_file"
echo "2. 检查 $ANALYSIS_DIR 目录中的CSV文件和排名表获取详细分析" >> "$summary_file"
echo "3. 对于性能表现不佳的模型，考虑重新训练或调整参数" >> "$summary_file"

echo "===========================================" 
echo "分析完成! 详细报告已保存至 $summary_file"
echo "CSV格式的比较结果保存在 $ANALYSIS_DIR 目录"
echo "建议运行 python generate_summary_plots.py 生成图表"
echo "===========================================" 