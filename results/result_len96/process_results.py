#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
处理结果文件，将格式从原始格式转换为 "模型_数据集_sl输入序列长度_pl预测序列长度" 格式
"""

import os
import re

# 输入和输出文件路径
input_file = "other_total.txt"
output_file = "other_total_new.txt"

# 确保当前工作目录是脚本所在目录
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# 创建结果列表
processed_lines = []

# 读取输入文件
with open(input_file, 'r', encoding='utf-8') as f:
    lines = f.readlines()

i = 0
while i < len(lines):
    line = lines[i].strip()
    
    # 跳过空行
    if not line:
        i += 1
        continue
    
    # 使用正则表达式解析每个结果行
    match = re.match(r'(\w+)_(\d+)_ema_(\w+)_(\w+)_ftM_sl(\d+)_ll\d+_pl(\d+)_Exp_\d+', line)
    
    if match:
        # 提取相关信息
        dataset = match.group(1)
        # pred_len = match.group(2)  # 这是原始格式中的预测长度，可能与pl不同
        model = match.group(3)
        dataset_repeat = match.group(4)  # 这是重复的数据集名称
        seq_len = match.group(5)
        pred_len = match.group(6)
        
        # 创建新格式
        new_format = f"{model}_{dataset}_sl{seq_len}_pl{pred_len}"
        processed_lines.append(new_format)
        
        # 检查下一行是否包含指标
        if i + 1 < len(lines) and "mse:" in lines[i + 1]:
            processed_lines.append(lines[i + 1].strip())
            processed_lines.append("")  # 添加空行分隔
            i += 2
        else:
            i += 1
    else:
        # 如果不匹配预期格式，保留原始行
        if line:
            processed_lines.append(line)
        i += 1

# 写入输出文件
with open(output_file, 'w', encoding='utf-8') as f:
    f.write('\n'.join(processed_lines))

print(f"处理完成！结果已保存到 {output_file}") 