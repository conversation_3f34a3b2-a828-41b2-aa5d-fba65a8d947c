#!/usr/bin/env python
import os
import re
import csv
from collections import defaultdict
import argparse

def process_model_logs(model_name=None):
    """
    Process log files in the logs/time directory and calculate average speeds.
    If model_name is provided, only process logs for that specific model.
    """
    # Directory containing log files
    log_dir = "logs/time"
    
    # Create the directory if it doesn't exist
    if not os.path.exists(log_dir):
        print(f"Error: Log directory {log_dir} doesn't exist")
        return
        
    # CSV file for results
    summary_file = "model_speed_summary.csv"
    
    # Regular expressions to extract speed information
    train_pattern = r"Average training forward pass time across all epochs: ([\d\.]+)s per batch"
    test_pattern = r"Test inference time \(forward pass only\): ([\d\.]+)s per batch"
    
    # List to store detailed results
    detailed_results = []
    
    # Process all log files
    files_processed = 0
    for filename in sorted(os.listdir(log_dir)):
        if not filename.endswith(".log"):
            continue
        
        # Extract model, dataset, and sequence length info from filename
        # Expected format: ModelName_DatasetName_InputLength_OutputLength.log
        # E.g., Amplifier_ETTh1_720_720.log
        
        # Remove the .log extension and split by underscore
        parts = filename[:-4].split("_")
        if len(parts) < 3:
            continue
        
        # Handle special cases for model names
        current_model = None
        dataset = None
        input_length = "N/A"
        output_length = "N/A"
        
        if parts[0] == "SparseTSF" and parts[1] == "mlp":
            current_model = "SparseTSF_mlp"
            dataset = parts[2]
            # Check if there are sequence length parameters
            if len(parts) >= 5:  # Account for model_dataset_in_out format
                input_length = parts[3]
                output_length = parts[4]
        elif parts[0] == "FITS" or parts[0] == "PaiFilter" or parts[0] == "TexFilter":
            # Special case for models with additional parameters in filename
            current_model = parts[0]
            dataset = parts[1]
            # Check if there are sequence length parameters
            if len(parts) >= 4:
                input_length = parts[2]
                output_length = parts[3]
        else:
            current_model = parts[0]
            dataset = parts[1]
            # Check if there are sequence length parameters
            if len(parts) >= 4:
                input_length = parts[2]
                output_length = parts[3]
        
        # Skip if not the requested model
        if model_name and current_model != model_name:
            continue
        
        # Read log file content
        try:
            with open(os.path.join(log_dir, filename), 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract training and test times
            train_match = re.search(train_pattern, content)
            test_match = re.search(test_pattern, content)
            
            if train_match and test_match:
                train_time = float(train_match.group(1))
                test_time = float(test_match.group(1))
                
                # Add to detailed results
                detailed_results.append({
                    "model": current_model,
                    "dataset": dataset,
                    "input_length": input_length,
                    "output_length": output_length,
                    "train_speed": train_time,
                    "test_speed": test_time,
                    "filename": filename
                })
                
                files_processed += 1
        except Exception as e:
            print(f"Error processing {filename}: {e}")
    
    # Generate summary table organized for research paper presentation
    if files_processed > 0:
        # First, find all unique models and dataset/input/output combinations
        all_models = sorted(set(r["model"] for r in detailed_results))
        all_configs = sorted(set((r["dataset"], r["input_length"], r["output_length"]) 
                                for r in detailed_results))
        
        # Create a nested dictionary to store the average speeds
        # Structure: {(dataset, input_len, output_len): {model: (train_speed, test_speed, num_runs)}}
        summary_data = defaultdict(dict)
        
        # Group all results by configuration and model
        for result in detailed_results:
            config = (result["dataset"], result["input_length"], result["output_length"])
            model = result["model"]
            
            if model not in summary_data[config]:
                summary_data[config][model] = (result["train_speed"], result["test_speed"], 1)
            else:
                prev_train, prev_test, prev_count = summary_data[config][model]
                new_count = prev_count + 1
                new_train = (prev_train * prev_count + result["train_speed"]) / new_count
                new_test = (prev_test * prev_count + result["test_speed"]) / new_count
                summary_data[config][model] = (new_train, new_test, new_count)
        
        # Write the paper-ready table to CSV
        with open(summary_file, 'w', newline='') as f:
            writer = csv.writer(f)
            
            # Create header row with model names and train/test columns
            header = ["Dataset", "Input Length", "Output Length"]
            for model in all_models:
                header.extend([f"{model} (Train)", f"{model} (Test)"])
            
            writer.writerow(header)
            
            # Write data rows
            for dataset, input_len, output_len in all_configs:
                row = [dataset, input_len, output_len]
                
                for model in all_models:
                    if model in summary_data[(dataset, input_len, output_len)]:
                        train_speed, test_speed, _ = summary_data[(dataset, input_len, output_len)][model]
                        row.extend([f"{train_speed:.6f}", f"{test_speed:.6f}"])
                    else:
                        row.extend(["-", "-"])  # No data for this model/config combination
                
                writer.writerow(row)
        
        print(f"Processed {files_processed} log files")
        print(f"Paper-ready summary table saved to {summary_file}")
    else:
        if model_name:
            print(f"No log files found for model: {model_name}")
        else:
            print("No log files found with the required metrics")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Calculate average training and test speeds from model logs")
    parser.add_argument("--model", help="Process logs for a specific model only")
    args = parser.parse_args()
    
    process_model_logs(args.model)