# 硬编码模型参数数据，替代从CSV读取
param_data = {
    'Model': ['Amplifier', 'DLinear', 'FITS', 'FreTS', 'iTransformer', 'SparseTSF', 'xPatch', 'MyModel', 'FilterNet'],
    'Dataset': ['electricity', 'electricity', 'electricity', 'electricity', 'electricity', 'electricity', 'electricity', 'electricity', 'electricity'],
    'Trainable Parameters': [520603, 18624, 1860, 3236832, 4833888, 1687, 144610, 1567104, 680674],
    'GPU Memory (MB)': [17.63818359375, 4.7021484375, 12.58154296875, 855.1640625, 111.248046875, 41.07568359375, 63.970703125, 56.4462890625, 36.1640625],
    'Input Length': [96, 96, 96, 96, 96, 96, 96, 96, 96],
    'Output Length': [96, 96, 96, 96, 96, 96, 96, 96, 96],
}

# 创建DataFrame
param_df = pd.DataFrame(param_data)

# 硬编码MSE值，替代从TXT读取
mse_from_txt = {
    ('Amplifier', 'electricity'): 0.147,
    ('FilterNet', 'electricity'): 0.183,
    ('FreTS', 'electricity'): 0.171,
    ('FITS', 'electricity'): 0.206,
    ('SparseTSF', 'electricity'): 0.201,
    ('DLinear', 'electricity'): 0.199,
    ('iTransformer', 'electricity'): 0.145,
    ('xPatch', 'electricity'): 0.162,
    ('MyModel', 'electricity'): 0.140,
}
