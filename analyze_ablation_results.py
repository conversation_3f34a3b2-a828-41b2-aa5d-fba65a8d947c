import re
import pandas as pd
import numpy as np

# Read the results file
with open('result_ablation.txt', 'r') as f:
    content = f.read()

# Parse the results
pattern = r'MyModel_(\w+)_sl(\d+)_pl(\d+)_(\w+)\s+mse:([\d\.]+), mae:([\d\.]+)'
matches = re.findall(pattern, content)

# Create a dataframe
data = []
for match in matches:
    dataset, sl, pl, method, mse, mae = match
    data.append({
        'dataset': dataset,
        'sequence_length': int(sl),
        'prediction_length': int(pl),
        'method': method,
        'mse': float(mse),
        'mae': float(mae)
    })

df = pd.DataFrame(data)

# List of methods and datasets for processing
methods = ['noX2', 'mlpX2', 'alphaX2']
datasets = df['dataset'].unique()
prediction_lengths = [96, 192, 336, 720]

# Create detailed results table by dataset, prediction length, and method
detailed_results = []
for dataset in datasets:
    for pl in prediction_lengths:
        for method in methods:
            subset = df[(df['dataset'] == dataset) & 
                         (df['prediction_length'] == pl) & 
                         (df['method'] == method)]
            
            if len(subset) > 0:
                mse = subset['mse'].values[0]
                mae = subset['mae'].values[0]
                detailed_results.append({
                    'dataset': dataset,
                    'prediction_length': pl,
                    'method': method,
                    'mse': mse,
                    'mae': mae
                })

detailed_df = pd.DataFrame(detailed_results)
detailed_df.to_csv('detailed_ablation_results.csv', index=False)

# Calculate averages by dataset and method
avg_by_dataset_method = []
for dataset in datasets:
    for method in methods:
        subset = df[(df['dataset'] == dataset) & (df['method'] == method)]
        
        if len(subset) > 0:
            avg_mse = subset['mse'].mean()
            avg_mae = subset['mae'].mean()
            avg_by_dataset_method.append({
                'dataset': dataset,
                'method': method,
                'avg_mse': avg_mse,
                'avg_mae': avg_mae
            })

avg_by_dataset_df = pd.DataFrame(avg_by_dataset_method)
avg_by_dataset_df.to_csv('avg_by_dataset_ablation_results.csv', index=False)

# Calculate overall averages by method
overall_avg = []
for method in methods:
    subset = df[df['method'] == method]
    
    if len(subset) > 0:
        avg_mse = subset['mse'].mean()
        avg_mae = subset['mae'].mean()
        overall_avg.append({
            'method': method,
            'avg_mse': avg_mse,
            'avg_mae': avg_mae
        })

overall_avg_df = pd.DataFrame(overall_avg)
overall_avg_df.to_csv('overall_avg_ablation_results.csv', index=False)

# Create a pivot table for easier visualization
pivot_mse = pd.pivot_table(detailed_df, 
                           values='mse', 
                           index=['dataset', 'prediction_length'], 
                           columns=['method'])

pivot_mae = pd.pivot_table(detailed_df, 
                           values='mae', 
                           index=['dataset', 'prediction_length'], 
                           columns=['method'])

# Save pivoted results
pivot_mse.to_csv('pivot_mse_ablation_results.csv')
pivot_mae.to_csv('pivot_mae_ablation_results.csv')

print("CSV files generated successfully:")
print("1. detailed_ablation_results.csv - All detailed results")
print("2. avg_by_dataset_ablation_results.csv - Average by dataset and method")
print("3. overall_avg_ablation_results.csv - Overall averages by method")
print("4. pivot_mse_ablation_results.csv - MSE results in pivot format")
print("5. pivot_mae_ablation_results.csv - MAE results in pivot format")