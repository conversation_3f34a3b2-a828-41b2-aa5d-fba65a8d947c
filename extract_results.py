import os
import re
import pandas as pd

# Base directory for the experiment logs
base_dir = 'logs/ablation/3.0 不同MLP隐藏层'

# List of hidden layer sizes
hidden_sizes = ['32', '64', '128', '256', '512', '1024', '2048']

# List of datasets and prediction lengths
datasets = ['ETTh1', 'solar', 'traffic', 'weather']
pred_lengths = ['96', '192', '336', '720']

# Create a list to store all results
results = []

# Regular expression pattern to extract MSE and MAE values
pattern = r'mse:([\d\.]+), mae:([\d\.]+)'

# Iterate through each hidden size directory
for hidden_size in hidden_sizes:
    dir_path = os.path.join(base_dir, hidden_size)
    
    # Check if directory exists
    if not os.path.exists(dir_path):
        print(f"Directory not found: {dir_path}")
        continue
    
    # Process each dataset and prediction length combination
    for dataset in datasets:
        for pred_len in pred_lengths:
            # Input sequence length is fixed at 96
            input_len = '96'
            
            # Construct the log file name
            log_file = f"MyModel_{dataset}_{input_len}_{pred_len}.log"
            log_path = os.path.join(dir_path, log_file)
            
            # Check if file exists
            if not os.path.exists(log_path):
                print(f"File not found: {log_path}")
                continue
            
            try:
                # Read the last line of the file
                with open(log_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    last_line = lines[-1].strip()
                
                # Extract MSE and MAE values using regex
                match = re.search(pattern, last_line)
                if match:
                    mse = float(match.group(1))
                    mae = float(match.group(2))
                    
                    # Add results to the list
                    results.append({
                        'hidden_size': int(hidden_size),
                        'pred_len': int(pred_len),
                        'dataset': dataset,
                        'mse': mse,
                        'mae': mae
                    })
                else:
                    print(f"Could not extract MSE and MAE from: {log_path}")
            except Exception as e:
                print(f"Error processing {log_path}: {e}")

# Create a DataFrame from the results
df = pd.DataFrame(results)

# Sort the DataFrame to ensure consistent order
df = df.sort_values(by=['hidden_size', 'pred_len', 'dataset'])

# Prepare the data structure
consolidated_data = []

# Find the best MSE and MAE values for each dataset and prediction length
best_values = {}
for dataset in datasets:
    for pred in sorted([int(p) for p in pred_lengths]):
        subset = df[(df['dataset'] == dataset) & (df['pred_len'] == pred)]
        if len(subset) > 0:
            best_mse = subset['mse'].min()
            best_mae = subset['mae'].min()
            best_values[(dataset, pred, 'mse')] = best_mse
            best_values[(dataset, pred, 'mae')] = best_mae

# Order by pred_len, then by hidden_size
for pred in sorted([int(p) for p in pred_lengths]):
    for hidden in sorted([int(h) for h in hidden_sizes]):
        # Get data for this hidden size and prediction length
        subset = df[(df['hidden_size'] == hidden) & (df['pred_len'] == pred)]
        
        if len(subset) == 0:
            continue
        
        # Create a row with indices
        row = {'pred_len': pred, 'hidden_size': hidden}
        
        # Add data for each dataset and metric, marking best values with *
        for dataset in datasets:
            dataset_row = subset[subset['dataset'] == dataset]
            if len(dataset_row) > 0:
                mse_value = dataset_row.iloc[0]['mse']
                mae_value = dataset_row.iloc[0]['mae']
                
                # Mark best values with an asterisk
                if mse_value == best_values.get((dataset, pred, 'mse')):
                    row[f'{dataset}_mse'] = f"{mse_value}*"
                else:
                    row[f'{dataset}_mse'] = mse_value
                    
                if mae_value == best_values.get((dataset, pred, 'mae')):
                    row[f'{dataset}_mae'] = f"{mae_value}*"
                else:
                    row[f'{dataset}_mae'] = mae_value
            else:
                row[f'{dataset}_mse'] = None
                row[f'{dataset}_mae'] = None
        
        consolidated_data.append(row)

# Create consolidated DataFrame
consolidated_df = pd.DataFrame(consolidated_data)

# Save to CSV
consolidated_csv_path = 'ablation_results_consolidated.csv'
consolidated_df.to_csv(consolidated_csv_path, index=False)
print(f"Consolidated results saved to {consolidated_csv_path} with best values marked with *") 