import re
import pandas as pd
import numpy as np

# Read the results file
with open('result_ablation.txt', 'r') as f:
    content = f.read()

# Parse the results
pattern = r'MyModel_(\w+)_sl(\d+)_pl(\d+)_(\w+)\s+mse:([\d\.]+), mae:([\d\.]+)'
matches = re.findall(pattern, content)

# Create a dataframe
data = []
for match in matches:
    dataset, sl, pl, method, mse, mae = match
    data.append({
        'Dataset': dataset,
        'PredLen': int(pl),
        'Method': method,
        'MSE': float(mse),
        'MAE': float(mae)
    })

df = pd.DataFrame(data)

# List of methods and datasets
methods = ['noX2', 'mlpX2', 'alphaX2']
datasets = df['Dataset'].unique()
prediction_lengths = [96, 192, 336, 720]

# Create results table in pivot format
pivot_df = pd.DataFrame()

# For each dataset and prediction length, get all methods' results
for dataset in datasets:
    dataset_rows = []
    
    # Process each prediction length
    for pl in prediction_lengths:
        row_data = {'Dataset': dataset, 'PredLen': pl}
        
        # Get results for each method
        for method in methods:
            subset = df[(df['Dataset'] == dataset) & 
                        (df['PredLen'] == pl) & 
                        (df['Method'] == method)]
            
            if len(subset) > 0:
                row_data[f'{method}_MSE'] = subset['MSE'].values[0]
                row_data[f'{method}_MAE'] = subset['MAE'].values[0]
        
        if len(row_data) > 2:  # If we have data beyond just Dataset and PredLen
            dataset_rows.append(row_data)
    
    # Calculate average for this dataset across prediction lengths
    if dataset_rows:
        avg_row = {'Dataset': dataset, 'PredLen': 'Avg'}
        
        for method in methods:
            mse_values = [row[f'{method}_MSE'] for row in dataset_rows if f'{method}_MSE' in row]
            mae_values = [row[f'{method}_MAE'] for row in dataset_rows if f'{method}_MAE' in row]
            
            if mse_values:
                avg_row[f'{method}_MSE'] = np.mean(mse_values)
            if mae_values:
                avg_row[f'{method}_MAE'] = np.mean(mae_values)
        
        dataset_rows.append(avg_row)
        
        # Add rows to final dataframe
        pivot_df = pd.concat([pivot_df, pd.DataFrame(dataset_rows)], ignore_index=True)

# Calculate overall averages across all datasets (excluding the dataset-specific averages)
overall_avg_row = {'Dataset': 'Overall', 'PredLen': 'Avg'}

for method in methods:
    method_mse_col = f'{method}_MSE'
    method_mae_col = f'{method}_MAE'
    
    # Filter out rows that are already averages
    non_avg_rows = pivot_df[pivot_df['PredLen'] != 'Avg']
    
    if method_mse_col in pivot_df.columns:
        overall_avg_row[method_mse_col] = non_avg_rows[method_mse_col].mean()
    if method_mae_col in pivot_df.columns:
        overall_avg_row[method_mae_col] = non_avg_rows[method_mae_col].mean()

# Add overall average row
pivot_df = pd.concat([pivot_df, pd.DataFrame([overall_avg_row])], ignore_index=True)

# Save to CSV
pivot_df.to_csv('ablation_results_table.csv', index=False)

print("Ablation results table saved to ablation_results_table.csv")