import torch
import torch.nn as nn
from layers.revin import RevIN


class moving_avg(nn.Module):
    """
    Moving average block to highlight the trend of time series
    """

    def __init__(self, kernel_size, stride):
        super(moving_avg, self).__init__()
        self.kernel_size = kernel_size
        self.avg = nn.AvgPool1d(kernel_size=kernel_size, stride=stride, padding=0)

    def forward(self, x):
        # padding on the both ends of time series
        front = x[:, 0:1, :].repeat(1, (self.kernel_size - 1) // 2, 1)
        end = x[:, -1:, :].repeat(1, (self.kernel_size - 1) // 2, 1)
        x = torch.cat([front, x, end], dim=1)
        x = self.avg(x.permute(0, 2, 1))
        x = x.permute(0, 2, 1)
        return x


class series_decomp(nn.Module):
    """
    Series decomposition block
    """

    def __init__(self, kernel_size):
        super(series_decomp, self).__init__()
        self.moving_avg = moving_avg(kernel_size, stride=1)

    def forward(self, x):
        moving_mean = self.moving_avg(x)
        res = x - moving_mean
        return res, moving_mean


class ComplexLinear(nn.Module):
    """
    实现复数线性层，但完全在实数域中操作以避免多GPU问题
    """

    def __init__(self, in_features, out_features):
        super(ComplexLinear, self).__init__()
        self.real_linear = nn.Linear(in_features, out_features)
        self.imag_linear = nn.Linear(in_features, out_features)

    def forward(self, x):
        """
        输入x应该是形如[batch, ..., features]的复数张量
        """
        x_real = x.real
        x_imag = x.imag

        # 应用复数乘法规则: (a+bi)(c+di) = (ac-bd) + (ad+bc)i
        real_part = self.real_linear(x_real) - self.imag_linear(x_imag)
        imag_part = self.real_linear(x_imag) + self.imag_linear(x_real)

        # 合并为复数结果
        return torch.complex(real_part, imag_part)


class Model(nn.Module):
    def __init__(self, configs):
        super(Model, self).__init__()
        self.seq_len = configs.seq_len
        self.pred_len = configs.pred_len
        self.channels = configs.enc_in
        self.hidden_size = configs.hidden_size
        self.revin_layer = RevIN(configs.enc_in, affine=True, subtract_last=False)

        kernel_size = 25
        self.decompsition = series_decomp(kernel_size)

        self.mask_matrix = nn.Parameter(torch.ones(int(self.seq_len / 2) + 1, self.channels))

        # 替换复数线性层为自定义复数层
        self.freq_linear = ComplexLinear(
            int(self.seq_len / 2) + 1,
            int(self.pred_len / 2) + 1
        )

        self.linear_seasonal = nn.Sequential(
            nn.Linear(self.seq_len, self.hidden_size),
            nn.LeakyReLU(),
            nn.Linear(self.hidden_size, self.pred_len)
        )

        self.linear_trend = nn.Sequential(
            nn.Linear(self.seq_len, self.hidden_size),
            nn.LeakyReLU(),
            nn.Linear(self.hidden_size, self.pred_len)
        )

        # SCI block
        self.SCI = configs.SCI
        self.extract_common_pattern = nn.Sequential(
            nn.Linear(self.channels, self.channels),
            nn.LeakyReLU(),
            nn.Linear(self.channels, 1)
        )

        self.model_common_pattern = nn.Sequential(
            nn.Linear(self.seq_len, self.hidden_size),
            nn.LeakyReLU(),
            nn.Linear(self.hidden_size, self.seq_len)
        )

        self.model_spacific_pattern = nn.Sequential(
            nn.Linear(self.seq_len, self.hidden_size),
            nn.LeakyReLU(),
            nn.Linear(self.hidden_size, self.seq_len)
        )

        # self.attention_merge = nn.Sequential(
        #     nn.Linear(2*self.channels, self.channels),
        #     nn.GELU(),
        #     nn.Dropout(0.4),
        #     nn.Linear(self.channels, self.channels),
        # )

        
        self.attention_mlp = nn.Sequential(
            nn.Linear(self.channels * 2, self.channels),
            nn.GELU(),
            nn.Dropout(0.4),
            nn.Linear(self.channels, self.channels),
            nn.Sigmoid()
        )


    def forward(self, x):  # x_mark_enc, x_dec, x_mark_dec, mask=None
        B, T, C = x.size()
        device = x.device

        # RevIN
        z = x
        z = self.revin_layer(z, 'norm')
        x = z

        # Energy Amplification Block
        x_fft = torch.fft.rfft(x, dim=1)  # domain conversion
        x_inverse_fft = torch.flip(x_fft, dims=[1])  # flip the spectrum
        x_inverse_fft = x_inverse_fft * self.mask_matrix
        x_amplifier_fft = x_fft + x_inverse_fft
        x_amplifier = torch.fft.irfft(x_amplifier_fft, dim=1)

        # SCI block
        if self.SCI:
            x = x_amplifier
            # extract common pattern
            common_pattern = self.extract_common_pattern(x)
            common_pattern = self.model_common_pattern(common_pattern.permute(0, 2, 1)).permute(0, 2, 1)
            # model specific pattern
            specififc_pattern = x - common_pattern.repeat(1, 1, C)
            specififc_pattern = self.model_spacific_pattern(specififc_pattern.permute(0, 2, 1)).permute(0, 2, 1)

            x = specififc_pattern + common_pattern.repeat(1, 1, C)
            x_amplifier = x

        # Seasonal Trend Forecaster
        seasonal, trend = self.decompsition(x_amplifier)
        seasonal = self.linear_seasonal(seasonal.permute(0, 2, 1)).permute(0, 2, 1)
        trend = self.linear_trend(trend.permute(0, 2, 1)).permute(0, 2, 1)

        # 1 add(orign)
        out_amplifier = seasonal + trend

        # 2 mlp
        # out_amplifier = self.attention_merge(torch.cat([seasonal, trend], dim=-1))

        # 4 attn
        # fusion_weights = self.attention_mlp(torch.cat([seasonal, trend], dim=-1))  # [B, pred_len, C] 
        # out_amplifier = fusion_weights * seasonal + (1 - fusion_weights) * trend  # [B, pred_len, C]

        # 4 attn
        # fusion_weights = self.attention_mlp(torch.cat([seasonal, trend], dim=-1))  # [B, pred_len, C] 
        # out_amplifier = 2*(fusion_weights * seasonal + (1 - fusion_weights) * trend)  # [B, pred_len, C]

        # Energy Restoration Block - 这是关键修改部分
        out_amplifier_fft = torch.fft.rfft(out_amplifier, dim=1)

        # 安全地应用频率线性层
        x_inverse_fft_permuted = x_inverse_fft.permute(0, 2, 1)  # [B, C, F]

        # 使用批处理方式安全处理
        batch_channel = B * C
        freq_dim = x_inverse_fft_permuted.size(2)

        # 将张量展平为[B*C, F]以进行批处理
        x_inverse_fft_flat = x_inverse_fft_permuted.reshape(batch_channel, freq_dim)

        # 应用自定义复数线性层
        x_inverse_fft_processed = self.freq_linear(x_inverse_fft_flat)

        # 重塑回[B, C, F_out]
        freq_out_dim = int(self.pred_len / 2) + 1
        x_inverse_fft_result = x_inverse_fft_processed.reshape(B, C, freq_out_dim)

        # 恢复原始维度顺序
        x_inverse_fft_out = x_inverse_fft_result.permute(0, 2, 1)  # [B, F_out, C]

        out_fft = out_amplifier_fft - x_inverse_fft_out
        out = torch.fft.irfft(out_fft, dim=1)

        # inverse RevIN
        z = out
        z = self.revin_layer(z, 'denorm')
        out = z

        return out