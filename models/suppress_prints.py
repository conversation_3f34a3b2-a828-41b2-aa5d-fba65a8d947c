import sys
import os

class PrintSuppressor:
    """
    用于在模型运行期间抑制打印输出的工具类
    用法:
    with PrintSuppressor():
        # 这里的代码中的print语句将被抑制
    """
    def __init__(self):
        self.original_stdout = None
        self.devnull = None
    
    def __enter__(self):
        # 保存原始stdout
        self.original_stdout = sys.stdout
        # 重定向stdout到null设备
        self.devnull = open(os.devnull, 'w')
        sys.stdout = self.devnull
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 恢复原始stdout
        sys.stdout = self.original_stdout
        # 关闭null设备
        if self.devnull:
            self.devnull.close() 