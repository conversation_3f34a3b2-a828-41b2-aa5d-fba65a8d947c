import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from layers.decomp import DECOMP
from layers.revin import RevIN
# from layers.fre_network import MultiscaleFreNetwork   # FreTS
# from layers.fre_network_V2 import EnhancedFreNetwork   # FreTS
# from layers.fre_network_V3 import MultiscaleFreNetwork
# from layers.fre_network_V3_3 import MultiscaleWaveletNetwork
# from layers.fre_network_V3_1 import MultiscaleFreNetwork
# from layers.fre_network_ori import FreNetwork
from layers.fre_network_V5 import FreNetwork
# from layers.fre_network_V8 import FreNetwork

# from sklearn.decomposition import PCA
# from models.FITS import Model as freModel   # FITS
import argparse




class Model(nn.Module):
    """
    Decomposition-Linear
    """
    def __init__(self, configs):
        super(Model, self).__init__()
        self.seq_len = configs.seq_len
        self.pred_len = configs.pred_len
        self.individual = configs.individual
        self.channels = configs.enc_in
        self.type = configs.model_type

        # Prediction refinement module
        self.use_refinement = True

        # # 添加MLP注意力机制
        self.attention_mlp = nn.Sequential(
            nn.Linear(self.channels * 2, self.channels),
            nn.GELU(),
            nn.Dropout(0.4),
            nn.Linear(self.channels, self.channels),
            nn.Sigmoid()
        )


        # self.attention_merge = nn.Sequential(
        #     nn.Linear(2*self.channels, self.channels),
        #     nn.GELU(),
        #     nn.Dropout(0.4),
        #     nn.Linear(self.channels, self.channels),
        # )

        # Normalization
        self.revin = configs.revin
        self.revin_layer = RevIN(configs.enc_in,affine=True,subtract_last=False)

        # Moving Average
        self.ma_type = configs.ma_type
        alpha = configs.alpha       # smoothing factor for EMA (Exponential Moving Average)
        beta = configs.beta         # smoothing factor for DEMA (Double Exponential Moving Average)

        self.decomp = DECOMP(self.ma_type, alpha, beta, kernel_size=25)
        self.Seasonal = FreNetwork(configs)

        cof = math.ceil((math.sqrt(self.channels))/5)

        if self.type == 'mlp':
            self.Trend = nn.Sequential(
                nn.Linear(self.seq_len, self.seq_len* cof),
                nn.Tanh(),
                nn.Dropout(0.4),
                nn.Linear(self.seq_len * cof, self.seq_len * cof),
                nn.Tanh(),
                nn.Dropout(0.4),
                nn.Linear(self.seq_len * cof, self.pred_len)
            )

            # self.Trend = nn.Sequential(
            #     nn.Linear(self.seq_len, 4096),
            #     nn.Tanh(),
            #     nn.Dropout(0.4),
            #     nn.Linear(4096, 4096),
            #     nn.Tanh(),
            #     nn.Dropout(0.4),
            #     nn.Linear(4096, self.pred_len)
            # )

        elif self.type == 'linear':
            self.Trend = nn.Sequential(
                nn.Linear(self.seq_len, self.pred_len)
            )
        else:
            raise ValueError(f"Invalid model type: {self.type}")


    def forward(self, x):
        # x: [Batch, Input length, Channel]
        # B, T, C = x.shape
        # Normalization
        if self.revin:
            x = self.revin_layer(x, 'norm')

        # 分解
        seasonal_init, trend_init = self.decomp(x)

        # 季节信号预测
        seasonal_output = self.Seasonal(seasonal_init)
        # 趋势信号预测
        trend_output = self.Trend(trend_init.permute(0, 2, 1)).permute(0, 2, 1)

        # 1. 直接相加
        # x = seasonal_output + trend_output

        # 2. mlp 融合
        # x = self.attention_merge(torch.cat([seasonal_output, trend_output], dim=-1))

        # 3. 能量不变融合        
        fusion_weights = self.attention_mlp(torch.cat([seasonal_output, trend_output], dim=-1))  # [B, pred_len, C] 
        x = 2*(fusion_weights * seasonal_output + (1 - fusion_weights) * trend_output)  # [B, pred_len, C]


        # Denormalization
        if self.revin:
            x = self.revin_layer(x, 'denorm')

        return  x