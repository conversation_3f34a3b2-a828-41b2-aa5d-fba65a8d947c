import torch
import torch.nn as nn
import argparse
import numpy as np
from models.MyModel import Model as MyModel
from models.xPatch import Model as xPatch
from models.iTransformer import Model as itransformer

class Config:
    def __init__(self):
        self.seq_len = 720
        self.pred_len = 720
        self.enc_in = 860
        self.patch_len = 6
        self.stride = 3
        self.padding_patch = True
        self.revin = True
        self.ma_type = 'ema'
        self.alpha = 0.3
        self.beta = 0.3
        self.individual = False
        self.FreMLP_embed_size = 8
        self.FreMLP_hidden_size = 256
        self.model_type = 'linear'
        self.output_attention = False
        # iTransformer specific attributes
        self.use_norm = False
        self.class_strategy = 'projection'
        self.d_model = 512
        self.embed = 'timeF'
        self.freq = 'h'
        self.dropout = 0.1
        self.factor = 1
        self.n_heads = 8
        self.d_ff = 2048
        self.activation = 'gelu'
        self.e_layers=4

def count_parameters(model):
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

def analyze_model(model, name):
    total_params = count_parameters(model)
    print(f"\n{'=' * 50}")
    print(f"{name} Model Analysis:")
    print(f"{'=' * 50}")
    print(f"Total Parameters: {total_params:,}")
    
    # Count parameters by layer type
    params_by_layer = {}
    for name, module in model.named_modules():
        if isinstance(module, (nn.Linear, nn.Conv1d, nn.Conv2d, nn.LSTM, nn.GRU)):
            layer_type = module.__class__.__name__
            params = sum(p.numel() for p in module.parameters() if p.requires_grad)
            if layer_type not in params_by_layer:
                params_by_layer[layer_type] = 0
            params_by_layer[layer_type] += params
    
    print("\nParameters by Layer Type:")
    for layer_type, params in params_by_layer.items():
        percentage = (params / total_params) * 100
        print(f"  {layer_type}: {params:,} ({percentage:.2f}%)")
    
    return total_params

def main():
    # Create configs
    configs = Config()
    
    # Create models
    mymodel = MyModel(configs)
    xpatch_model = xPatch(configs)
    itransformer_model = itransformer(configs)

    
    # Analyze models
    my_params = analyze_model(mymodel, "MyModel")
    xpatch_params = analyze_model(xpatch_model, "xPatch")
    itransformer_params = analyze_model(itransformer_model, "iTransformer")

    
    # Compare models
    print(f"\n{'=' * 50}")
    print("Model Comparison:")
    print(f"{'=' * 50}")
    print(f"MyModel Parameters: {my_params:,}")
    print(f"xPatch Parameters: {xpatch_params:,}")
    print(f"iTransformer Parameters: {itransformer_params:,}")
    
    # Compare MyModel and xPatch
    difference = abs(my_params - xpatch_params)
    percentage = (difference / min(my_params, xpatch_params)) * 100
    
    if my_params > xpatch_params:
        comparison = f"MyModel has {difference:,} more parameters than xPatch ({percentage:.2f}% more)"
    elif xpatch_params > my_params:
        comparison = f"xPatch has {difference:,} more parameters than MyModel ({percentage:.2f}% more)"
    else:
        comparison = "MyModel and xPatch have the same number of parameters"
    
    print(comparison)
    
    # Compare MyModel and iTransformer
    difference = abs(my_params - itransformer_params)
    percentage = (difference / min(my_params, itransformer_params)) * 100
    
    if my_params > itransformer_params:
        comparison = f"MyModel has {difference:,} more parameters than iTransformer ({percentage:.2f}% more)"
    elif itransformer_params > my_params:
        comparison = f"iTransformer has {difference:,} more parameters than MyModel ({percentage:.2f}% more)"
    else:
        comparison = "MyModel and iTransformer have the same number of parameters"
    
    print(comparison)

if __name__ == "__main__":
    main() 