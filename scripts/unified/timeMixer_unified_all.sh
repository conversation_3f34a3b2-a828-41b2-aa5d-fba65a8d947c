#!/bin/bash
ma_type=ema
alpha=0.3
beta=0.3
result_file="timeMixer_unified_result.txt"

if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/last" ]; then
    mkdir ./logs/last
fi

# 配置参数
seq_len=96
model_type='mlp' # mlp


# 信号处理 - 添加清理函数
cleanup() {
    echo "Caught interrupt signal. Terminating all running tasks..."
    # 杀死所有子进程
    pkill -P $$
    # 给进程一点时间来优雅终止
    sleep 2
    # 如果还有子进程存活，强制终止
    pkill -9 -P $$ 2>/dev/null
    echo "All tasks terminated."
    exit 1
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 检测可用的GPU数量
NUM_GPUS=$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)
echo "Detected $NUM_GPUS available GPUs"

# 如果没有检测到GPU，默认设置为1以避免错误
if [ "$NUM_GPUS" -eq 0 ]; then
    echo "Warning: No GPUs detected, defaulting to 1"
    NUM_GPUS=1
fi

# 创建任务队列
declare -a model_array=("TimeMixer")
# declare -a pred_len_array=(96 192 336 720)
declare -a pred_len_array=(720 336 192 96)
# declare -a pred_len_array=(24 36 48 60)
declare -a data_array=("traffic" "electricity" "solar" "weather" "ETTh1" "ETTh2" "ETTm1" "ETTm2" "exchange")
declare -a gpu_id=("0" "1" "2" "3")

# 创建所有任务组合
tasks=()

for data_name in "${data_array[@]}"; do
    for model_name in "${model_array[@]}"; do
        for pred_len in "${pred_len_array[@]}"; do
            tasks+=("$model_name $pred_len $data_name")
        done
    done
done

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 运行任务的函数
run_task() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    
    
    echo "Starting task on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 根据数据集设置适当的参数
    local data_path=""
    local enc_in=""
    local batch_size=""
    local base_learning_rate=""  # 数据集的基准学习率
    local learning_rate=""       # 根据预测长度调整后的学习率
    local data_type="custom"
    local cycle_param="--cycle 24"
    

    if [[ $data_name == "traffic" ]]; then
        data_path="traffic.csv"
        data_type="custom"
        enc_in=862
        batch_size=8
        base_learning_rate=0.01
        d_model=32
        d_ff=64
        e_layers=3
        down_sampling_layers=3
        down_sampling_window=2
        FreMLP_hidden_size=256
        freMLP_embed_size=8

    elif [[ $data_name == "solar" ]]; then
        data_path="solar.txt"
        data_type="Solar"
        enc_in=137
        batch_size=32
        base_learning_rate=0.001
        d_model=512
        d_ff=2048
        e_layers=3
        down_sampling_layers=2
        down_sampling_window=2
        cycle_param=""
        FreMLP_hidden_size=256
        freMLP_embed_size=8
        # 使用默认的timeF embedding，与官方TimeMixer一致
    
    elif [[ $data_name == "ETTh1" ]]; then
        data_path="ETTh1.csv"
        data_type="ETTh1"
        enc_in=7
        batch_size=128
        base_learning_rate=0.01
        d_model=16
        d_ff=32
        e_layers=2
        down_sampling_layers=3
        down_sampling_window=2
        FreMLP_hidden_size=128
        freMLP_embed_size=4

    elif [[ $data_name == "ETTh2" ]]; then
        data_path="ETTh2.csv"
        data_type="ETTh2"
        enc_in=7
        batch_size=16
        base_learning_rate=0.01
        d_model=16
        d_ff=32
        e_layers=2
        down_sampling_layers=3
        down_sampling_window=2
        FreMLP_hidden_size=128
        freMLP_embed_size=4

    elif [[ $data_name == "ETTm1" ]]; then
        data_path="ETTm1.csv"
        data_type="ETTm1"
        enc_in=7
        batch_size=16
        base_learning_rate=0.01
        d_model=16
        d_ff=32
        e_layers=2
        down_sampling_layers=3
        down_sampling_window=2
        FreMLP_hidden_size=128
        freMLP_embed_size=4

    elif [[ $data_name == "ETTm2" ]]; then
        data_path="ETTm2.csv"
        data_type="ETTm2"
        enc_in=7
        batch_size=128
        base_learning_rate=0.01
        d_model=32
        d_ff=32
        e_layers=2
        down_sampling_layers=3
        down_sampling_window=2
        FreMLP_hidden_size=128
        freMLP_embed_size=4

    elif [[ $data_name == "exchange" ]]; then
        data_path="exchange_rate.csv"
        data_type="custom"
        enc_in=8
        batch_size=32
        base_learning_rate=0.00001
        d_model=16
        d_ff=32
        e_layers=3
        down_sampling_layers=3
        down_sampling_window=2
        FreMLP_hidden_size=128
        freMLP_embed_size=4

    elif [[ $data_name == "weather" ]]; then
        data_path="weather.csv"
        data_type="custom"
        enc_in=21
        batch_size=128
        base_learning_rate=0.01
        d_model=16
        d_ff=32
        e_layers=3
        down_sampling_layers=3
        down_sampling_window=2
        FreMLP_hidden_size=256
        freMLP_embed_size=8

    elif [[ $data_name == "electricity" ]]; then
        data_path="electricity.csv"
        data_type="custom"
        enc_in=321
        batch_size=32
        base_learning_rate=0.01
        d_model=16
        d_ff=32
        e_layers=3
        down_sampling_layers=3
        down_sampling_window=2
        FreMLP_hidden_size=256
        freMLP_embed_size=8

    elif [[ $data_name == "ili" ]]; then
        data_path="national_illness.csv"
        enc_in=7
        batch_size=32
        base_learning_rate=0.05
        seq_len=36
        cycle_param="" 

        # ILI数据集特殊处理
        if [[ $pred_len -eq 24 ]]; then
            learning_rate=$base_learning_rate
        elif [[ $pred_len -eq 36 ]]; then
            learning_rate=$(awk -v base="$base_learning_rate" 'BEGIN {printf "%.8f", base * 0.8}')
        elif [[ $pred_len -eq 48 ]]; then
            learning_rate=$(awk -v base="$base_learning_rate" 'BEGIN {printf "%.8f", base * 0.6}')
        elif [[ $pred_len -eq 60 ]]; then
            learning_rate=$(awk -v base="$base_learning_rate" 'BEGIN {printf "%.8f", base * 0.4}')
        fi
    fi

       
    # 对其他数据集根据预测长度调整学习率
    if [[ $data_name != "ili" && $data_name != "exchange" ]]; then
        if [[ $pred_len -eq 96 ]]; then
            learning_rate=$base_learning_rate
        elif [[ $pred_len -eq 192 ]]; then
            learning_rate=$(awk -v base="$base_learning_rate" 'BEGIN {printf "%.8f", base * 0.8}')
        elif [[ $pred_len -eq 336 ]]; then
            learning_rate=$(awk -v base="$base_learning_rate" 'BEGIN {printf "%.8f", base * 0.6}')
        elif [[ $pred_len -eq 720 ]]; then
            learning_rate=$(awk -v base="$base_learning_rate" 'BEGIN {printf "%.8f", base * 0.4}')
        fi
    elif [[ $data_name == "exchange" ]]; then
        # exchange数据集使用固定的基础学习率
        learning_rate=$base_learning_rate
    fi



    # 打印调整后的学习率
    echo "Base learning rate: $base_learning_rate, Adjusted learning rate: $learning_rate"
    
    
    local log_file="logs/last/${model_name}_${data_name}_${seq_len}_${pred_len}.log"

    # 运行任务
    if [[ $data_name == "ili" ]]; then
        # 运行ILI任务
        CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
          --is_training 1 \
          --root_path ./dataset/ \
          --data_path $data_path \
          --model_id ${model_name}_${data_name}_sl${seq_len}_pl${pred_len}\
          --model $model_name \
          --data custom \
          --features M \
          --seq_len $seq_len \
          --label_len 18 \
          --pred_len $pred_len \
          --enc_in $enc_in \
          --des 'Exp' \
          --itr 1 \
          --batch_size $batch_size \
          --learning_rate $learning_rate \
          --lradj 'type3' \
          --patience 100 \
          --ma_type $ma_type \
          --alpha $alpha \
          --beta $beta \
          --FreMLP_embed_size 4 \
          --FreMLP_hidden_size 128 \
          --model_type $model_type \
          --result_file $result_file \
          --gpu 0 > $log_file 2>&1
    elif [[ $data_name == "solar" ]]; then
        # 运行Solar任务，使用官方TimeMixer参数配置
        CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
          --task_name long_term_forecast \
          --is_training 1 \
          --root_path ./dataset/ \
          --data_path $data_path \
          --model_id ${model_name}_${data_name}_sl${seq_len}_pl${pred_len}\
          --model $model_name \
          --data $data_type \
          --features M \
          --seq_len $seq_len \
          --label_len 0 \
          --pred_len $pred_len \
          --e_layers $e_layers \
          --enc_in $enc_in \
          --c_out $enc_in \
          --des 'Exp' \
          --itr 1 \
          --use_norm 0 \
          --d_model $d_model \
          --d_ff $d_ff \
          --channel_independence 0 \
          --batch_size $batch_size \
          --learning_rate $learning_rate \
          --lradj 'sigmoid' \
          --down_sampling_layers $down_sampling_layers \
          --down_sampling_method avg \
          --down_sampling_window $down_sampling_window \
          --result_file $result_file \
          --gpu 0 > $log_file 2>&1
    else
        # 运行其他数据集任务，使用官方TimeMixer参数配置
        CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
          --task_name long_term_forecast \
          --is_training 1 \
          --root_path ./dataset/ \
          --data_path $data_path \
          --model_id ${model_name}_${data_name}_sl${seq_len}_pl${pred_len}\
          --model $model_name \
          --data $data_type \
          --features M \
          --seq_len $seq_len \
          --label_len 0 \
          --pred_len $pred_len \
          --e_layers $e_layers \
          --enc_in $enc_in \
          --c_out $enc_in \
          --des 'Exp' \
          --itr 1 \
          --d_model $d_model \
          --d_ff $d_ff \
          --batch_size $batch_size \
          --learning_rate $learning_rate \
          --lradj 'sigmoid' \
          --down_sampling_layers $down_sampling_layers \
          --down_sampling_method avg \
          --down_sampling_window $down_sampling_window \
          --result_file $result_file \
          --gpu 0 > $log_file 2>&1
    fi
    
    local exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Task on GPU $gpu_id failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Task completed successfully on GPU $gpu_id"
    fi
    
    echo "Task completed on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len (took ${duration}s)"
}

# 直接分配任务到GPU，使用循环分配方式
echo "Starting task allocation in round-robin fashion..."
task_index=0

while [ $task_index -lt $total_tasks ]; do
    for gpu in "${gpu_id[@]}"; do
        if [ $task_index -lt $total_tasks ]; then
            # 解析任务
            IFS=' ' read -r model_name pred_len data_name <<< "${tasks[$task_index]}"
            
            # 在后台运行任务
            run_task $gpu "$model_name" "$pred_len" "$data_name" &
            
            # 增加任务索引
            task_index=$((task_index + 1))
            echo "Assigned task $task_index/$total_tasks to GPU $gpu"
        else
            # 没有更多任务
            break
        fi
    done
    
    # 如果所有任务已分配，跳出循环
    if [ $task_index -ge $total_tasks ]; then
        break
    fi
done

# 等待所有任务完成
echo "All tasks have been assigned. Waiting for completion..."
wait
echo "All tasks completed successfully!"

