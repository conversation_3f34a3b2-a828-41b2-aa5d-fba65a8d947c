#!/bin/bash
ma_type=ema
alpha=0.3
beta=0.3

result_file="SparseTSF_mlp_result_last.txt"

if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/last" ]; then
    mkdir ./logs/last
fi

model_name=SparseTSF
seq_len=96  # 使用原文的序列长度值

# 信号处理 - 添加清理函数
cleanup() {
    echo "Caught interrupt signal. Terminating all running tasks..."
    # 杀死所有子进程
    pkill -P $$
    # 给进程一点时间来优雅终止
    sleep 2
    # 如果还有子进程存活，强制终止
    pkill -9 -P $$ 2>/dev/null
    echo "All tasks terminated."
    exit 1
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 检测可用的GPU数量
NUM_GPUS=$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)
echo "Detected $NUM_GPUS available GPUs"

# 如果没有检测到GPU，默认设置为1以避免错误
if [ "$NUM_GPUS" -eq 0 ]; then
    echo "Warning: No GPUs detected, defaulting to 1"
    NUM_GPUS=1
fi

# 定义GPU ID数组，与MyModel脚本保持一致
declare -a gpu_id=("0" "1" "2" "3")
NUM_GPUS=${#gpu_id[@]}

# 创建任务队列 - 改为先遍历数据集，再遍历预测长度
declare -a tasks=()

# # ETTh1 - 使用原文MLP参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7 256 0.002 24 30 5 mlp 128")
# done

# # ETTh2 - 使用原文MLP参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len ETTh2 ETTh2.csv ETTh2 7 128 0.001 24 30 5 mlp 128")
# done

# # ETTm1 - 使用原文MLP参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len ETTm1 ETTm1.csv ETTm1 7 256 0.001 4 30 5 mlp 128")
# done

# # ETTm2 - 使用原文MLP参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len ETTm2 ETTm2.csv ETTm2 7 256 0.001 4 30 5 mlp 128")
# done


# exchange - 使用原文MLP参数
for pred_len in 96 192 336 720
do
    tasks+=("$model_name $pred_len exchange exchange_rate.csv custom 8 32 0.00001 24 30 5 mlp 128")
done

# # Weather - 使用原文MLP参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len weather weather.csv custom 21 256 0.05 4 30 5 mlp 128")
# done

# # Electricity - 使用原文MLP参数 - 按照用户要求调整顺序
# for pred_len in 720 336 192 96
# do
#     tasks+=("$model_name $pred_len electricity electricity.csv custom 321 128 0.02 24 30 5 mlp 128")
# done

# # Solar - 使用原文MLP参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len solar solar.txt Solar 137 256 0.01 4 30 5 mlp 512")
# done

# Traffic - 使用原文MLP参数 (已注释)
# for pred_len in 720 336 192 96
# do
#     tasks+=("$model_name $pred_len traffic traffic.csv custom 862 128 0.01 24 30 5 mlp 512")
# done

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 添加调试输出，检查任务内容
echo "Task format check:"
echo "${tasks[0]}"

# 运行任务的函数
run_task() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    local data_path=$5
    local data_type=$6
    local enc_in=$7
    local batch_size=$8
    local learning_rate=$9
    local period_len=${10}
    local train_epochs=${11}
    local patience=${12}
    local model_type=${13}
    local d_model=${14}
    
    # 确认参数均已正确传递
    echo "Parameters debug:"
    echo "  model_name: $model_name"
    echo "  pred_len: $pred_len"
    echo "  data_name: $data_name"
    echo "  model_type: $model_type"
    echo "  d_model: $d_model"
    
    local log_file="logs/last/${model_name}_${model_type}_${data_name}_${seq_len}_${pred_len}.log"
    echo "  log_file: $log_file"
    
    echo "Starting task on GPU $gpu_id: $model_name ($model_type) on $data_name with pred_len $pred_len"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 运行任务
    CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
      --is_training 1 \
      --root_path ./dataset/ \
      --data_path $data_path \
      --model_id ${model_name}_${data_name}_sl${seq_len}_pl${pred_len} \
      --model $model_name \
      --data $data_type \
      --features M \
      --seq_len $seq_len \
      --pred_len $pred_len \
      --period_len $period_len \
      --model_type $model_type \
      --d_model $d_model \
      --enc_in $enc_in \
      --train_epochs $train_epochs \
      --patience $patience \
      --batch_size $batch_size \
      --learning_rate $learning_rate \
      --des 'Exp' \
      --itr 1 \
      --ma_type $ma_type \
      --alpha $alpha \
      --beta $beta \
      --result_file $result_file \
      --gpu 0 > $log_file 2>&1
    
    local exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Task on GPU $gpu_id failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Task completed successfully on GPU $gpu_id"
    fi
    
    echo "Task completed on GPU $gpu_id: $model_name ($model_type) on $data_name with pred_len $pred_len (took ${duration}s)"
}

# 显示任务分配方式
echo "Distributing tasks across ${NUM_GPUS} GPUs in a round-robin fashion"

# 启动进程数组
pids=()

# 直接分配任务到GPU上
for ((i=0; i<$total_tasks; i++)); do
    # 计算应该使用哪个GPU
    gpu_idx=$((i % NUM_GPUS))
    current_gpu=${gpu_id[$gpu_idx]}
    
    # 解析任务
    IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in batch_size learning_rate period_len train_epochs patience model_type d_model <<< "${tasks[$i]}"
    
    # 在后台运行任务
    run_task $current_gpu "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" "$batch_size" "$learning_rate" "$period_len" "$train_epochs" "$patience" "$model_type" "$d_model" &
    pids+=($!)
    
    # 简单的进度显示
    echo "Started task $((i+1))/$total_tasks on GPU $current_gpu"
    
    # 如果已经分配了所有GPU的任务，稍微等待一会再继续分配
    # 这可以防止一下子启动太多进程
    if (( (i+1) % NUM_GPUS == 0 )); then
        echo "Waiting for current batch of tasks to initialize before continuing..."
        sleep 2
    fi
done

# 等待所有任务完成
echo "All tasks have been started. Waiting for completion..."
wait
echo "All tasks completed successfully!"
