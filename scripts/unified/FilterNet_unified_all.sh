#!/bin/bash
ma_type=ema
alpha=0.3
beta=0.3

result_file="FilterNet_result_last.txt"

if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/last" ]; then
    mkdir ./logs/last
fi


seq_len=96
label_len=48

# 信号处理 - 添加清理函数
cleanup() {
    echo "Caught interrupt signal. Terminating all running tasks..."
    # 杀死所有子进程
    pkill -P $$
    # 给进程一点时间来优雅终止
    sleep 2
    # 如果还有子进程存活，强制终止
    pkill -9 -P $$ 2>/dev/null
    echo "All tasks terminated."
    exit 1
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 检测可用的GPU数量
NUM_GPUS=$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)
echo "Detected $NUM_GPUS available GPUs"

# 如果没有检测到GPU，默认设置为1以避免错误
if [ "$NUM_GPUS" -eq 0 ]; then
    echo "Warning: No GPUs detected, defaulting to 1"
    NUM_GPUS=1
fi

# 定义GPU ID数组，与MyModel脚本保持一致
declare -a gpu_id=("0" "1" "2" "3")
NUM_GPUS=${#gpu_id[@]}

# 创建任务队列
tasks=()

# 添加所有任务到队列

# ETTh1 - 启用ETTh1任务
for pred_len in 96 192 336 720
do
    model_name=PaiFilter
    hidden_size=256
    batch_size=16
    learning_rate=0.005
    
    if [[ $pred_len -eq 192 ]]; then
        hidden_size=128
        batch_size=8
        learning_rate=0.001
    elif [[ $pred_len -eq 336 ]]; then
        hidden_size=128
        batch_size=64
        learning_rate=0.001
    elif [[ $pred_len -eq 720 ]]; then
        hidden_size=256
        batch_size=64
        learning_rate=0.001
    fi
    
    tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7 $batch_size $learning_rate $hidden_size")
done

# ETTh2 - 启用ETTh2任务
for pred_len in 96 192 336 720
do
    model_name=PaiFilter
    hidden_size=256
    batch_size=16
    learning_rate=0.005
    
    if [[ $pred_len -eq 192 ]]; then
        hidden_size=128
        batch_size=8
        learning_rate=0.001
    elif [[ $pred_len -eq 336 ]]; then
        hidden_size=128
        batch_size=64
        learning_rate=0.001
    elif [[ $pred_len -eq 720 ]]; then
        hidden_size=256
        batch_size=64
        learning_rate=0.001
    fi
    
    tasks+=("$model_name $pred_len ETTh2 ETTh2.csv ETTh2 7 $batch_size $learning_rate $hidden_size")
done

# ETTm1 - 启用ETTm1任务
for pred_len in 96 192 336 720
do
    model_name=PaiFilter
    hidden_size=256
    batch_size=32
    learning_rate=0.01
    
    tasks+=("$model_name $pred_len ETTm1 ETTm1.csv ETTm1 7 $batch_size $learning_rate $hidden_size")
done

# ETTm2 - 启用ETTm2任务
for pred_len in 96 192 336 720
do
    model_name=PaiFilter
    hidden_size=128
    batch_size=32
    learning_rate=0.005
    
    tasks+=("$model_name $pred_len ETTm2 ETTm2.csv ETTm2 7 $batch_size $learning_rate $hidden_size")
done

# Exchange - 启用Exchange任务
for pred_len in 96 192 336 720
do
    model_name=PaiFilter
    hidden_size=256
    batch_size=32
    learning_rate=0.005
    
    if [[ $pred_len -eq 192 ]]; then
        hidden_size=128
        learning_rate=0.00001
    elif [[ $pred_len -eq 336 ]]; then
        hidden_size=128
        learning_rate=0.00001
    elif [[ $pred_len -eq 720 ]]; then
        hidden_size=256
        learning_rate=0.00001
    fi
    
    tasks+=("$model_name $pred_len exchange exchange_rate.csv custom 8 $batch_size $learning_rate $hidden_size")
done

# Electricity - 启用Electricity任务
for pred_len in 96 192 336 720
do
    model_name=TexFilter
    hidden_size=512
    batch_size=256
    learning_rate=0.001
    embed_size=512
    dropout=0
    train_epochs=20
    patience=6
    
    tasks+=("$model_name $pred_len electricity electricity.csv custom 321 $batch_size $learning_rate $hidden_size $embed_size $dropout $train_epochs $patience")
done

# Traffic - 启用Traffic任务
for pred_len in 96 192 336 720
do
    model_name=TexFilter
    embed_size=256
    dropout=0
    train_epochs=20
    batch_size=16
    patience=6
    learning_rate=0.005
    hidden_size=512
    
    # 对于720使用不同的hidden_size
    if [[ $pred_len -eq 720 ]]; then
        hidden_size=1024
    fi
    
    tasks+=("$model_name $pred_len traffic traffic.csv custom 862 $batch_size $learning_rate $hidden_size $embed_size $dropout $train_epochs $patience")
done

# Weather - 启用Weather任务
for pred_len in 96 192 336 720
do
    model_name=TexFilter
    embed_size=128
    hidden_size=128
    dropout=0
    train_epochs=20
    batch_size=128
    patience=6
    learning_rate=0.01
    
    tasks+=("$model_name $pred_len weather weather.csv custom 21 $batch_size $learning_rate $hidden_size $embed_size $dropout $train_epochs $patience")
done

# Solar - 启用Solar任务
for pred_len in 96 192 336 720
do
    model_name=TexFilter
    embed_size=256
    dropout=0
    train_epochs=100
    batch_size=512
    patience=10
    learning_rate=0.005
    hidden_size=512
    
    # 对于720使用不同的hidden_size
    if [[ $pred_len -eq 720 ]]; then
        hidden_size=1024
    fi
    
    tasks+=("$model_name $pred_len solar solar.txt Solar 137 $batch_size $learning_rate $hidden_size $embed_size $dropout $train_epochs $patience")
done



# 注释掉其他数据集任务

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 运行任务的函数
run_task() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    local data_path=$5
    local data_type=$6
    local enc_in=$7
    local batch_size=$8
    local learning_rate=$9
    local hidden_size=${10}
    local embed_size=${11}
    local dropout=${12}
    local train_epochs=${13}
    local patience=${14}
    local log_file="logs/last/${model_name}_${data_name}_${seq_len}_${pred_len}.log"
    
    echo "Starting task on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 构建命令参数
    local cmd="CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
      --task_name long_term_forecast \
      --is_training 1 \
      --root_path ./dataset/ \
      --data_path $data_path \
      --model_id ${model_name}_${data_name}_sl${seq_len}_pl${pred_len} \
      --model $model_name \
      --data $data_type \
      --features M \
      --enc_in $enc_in \
      --seq_len $seq_len \
      --label_len $label_len \
      --pred_len $pred_len \
      --hidden_size $hidden_size \
      --batch_size $batch_size \
      --learning_rate $learning_rate \
      --des 'Exp' \
      --itr 1 \
      --ma_type $ma_type \
      --alpha $alpha \
      --beta $beta \
      --result_file $result_file \
      --gpu 0"
    
    # 仅添加存在且非空的可选参数
    if [[ -n "$embed_size" && "$embed_size" != '""' ]]; then
        cmd="$cmd --embed_size $embed_size"
    fi
    
    if [[ -n "$dropout" && "$dropout" != '""' ]]; then
        cmd="$cmd --dropout $dropout"
    fi
    
    if [[ -n "$train_epochs" && "$train_epochs" != '""' ]]; then
        cmd="$cmd --train_epochs $train_epochs"
    fi
    
    if [[ -n "$patience" && "$patience" != '""' ]]; then
        cmd="$cmd --patience $patience"
    fi
    
    # 运行命令并重定向输出
    eval "$cmd > $log_file 2>&1"
    
    local exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Task on GPU $gpu_id failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Task completed successfully on GPU $gpu_id"
    fi
    
    echo "Task completed on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len (took ${duration}s)"
}

# 显示任务分配方式
echo "Distributing tasks across ${NUM_GPUS} GPUs in a round-robin fashion"

# 启动进程数组
pids=()

# 直接分配任务到GPU上
for ((i=0; i<$total_tasks; i++)); do
    # 计算应该使用哪个GPU
    gpu_idx=$((i % NUM_GPUS))
    current_gpu=${gpu_id[$gpu_idx]}
    
    # 解析任务
    IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in batch_size learning_rate hidden_size embed_size dropout train_epochs patience <<< "${tasks[$i]}"
    
    # 在后台运行任务
    run_task $current_gpu "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" "$batch_size" "$learning_rate" "$hidden_size" "$embed_size" "$dropout" "$train_epochs" "$patience" &
    pids+=($!)
    
    # 简单的进度显示
    echo "Started task $((i+1))/$total_tasks on GPU $current_gpu"
    
    # 如果已经分配了所有GPU的任务，稍微等待一会再继续分配
    # 这可以防止一下子启动太多进程
    if (( (i+1) % NUM_GPUS == 0 )); then
        echo "Waiting for current batch of tasks to initialize before continuing..."
        sleep 2
    fi
done

# 等待所有任务完成
echo "All tasks have been started. Waiting for completion..."
wait
echo "All tasks completed successfully!"
