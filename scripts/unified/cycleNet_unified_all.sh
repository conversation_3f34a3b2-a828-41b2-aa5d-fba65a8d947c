#!/bin/bash
ma_type=ema
alpha=0.3
beta=0.3

result_file="cycleNet_unified_result.txt"

if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/"$ma_type ]; then
    mkdir ./logs/$ma_type
fi

model_name=CycleNet
seq_len=96  # 所有脚本都使用96作为序列长度

# 信号处理 - 添加清理函数
cleanup() {
    echo "Caught interrupt signal. Terminating all running tasks..."
    # 杀死所有子进程
    pkill -P $$
    # 给进程一点时间来优雅终止
    sleep 2
    # 如果还有子进程存活，强制终止
    pkill -9 -P $$ 2>/dev/null
    echo "All tasks terminated."
    exit 1
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 检测可用的GPU数量
NUM_GPUS=$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)
echo "Detected $NUM_GPUS available GPUs"

# 如果没有检测到GPU，默认设置为1以避免错误
if [ "$NUM_GPUS" -eq 0 ]; then
    echo "Warning: No GPUs detected, defaulting to 1"
    NUM_GPUS=1
fi

# 创建任务队列 - 使用原文的参数
declare -a tasks=()

# # ETTh1 - 使用原文参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7 256 0.005 24 30 5 mlp 0 0")
# done

# # ETTh2 - 使用原文参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len ETTh2 ETTh2.csv ETTh2 7 256 0.005 24 30 5 mlp 0 0")
# done

# # ETTm1 - 使用原文参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len ETTm1 ETTm1.csv ETTm1 7 256 0.005 96 30 5 mlp 0 0")
# done

# # ETTm2 - 使用原文参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len ETTm2 ETTm2.csv ETTm2 7 256 0.005 96 30 5 mlp 0 0")
# done

# # Weather - 使用原文参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len weather weather.csv custom 21 256 0.005 144 30 5 mlp 0 0")
# done

# # Electricity - 使用原文参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len electricity electricity.csv custom 321 64 0.005 168 30 5 mlp 0 0")
# done

# # Solar - 使用原文参数
# for pred_len in 96 192 336 720
# do
#     # Solar特别添加了use_revin参数
#     tasks+=("$model_name $pred_len solar solar.txt Solar 137 64 0.01 144 30 5 mlp 0 1")
# done

# # Traffic - 使用原文参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len traffic traffic.csv custom 862 64 0.002 168 30 5 mlp 0 0")
# done


# exchange - 使用原文参数
for pred_len in 96 192 336 720
do
    tasks+=("$model_name $pred_len exchange exchange_rate.csv custom 8 32 0.00001 24 30 5 mlp 0 0")
done

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 添加调试输出，检查任务内容
echo "Task format check:"
echo "${tasks[0]}"

# 为每个GPU准备任务列表 - 均衡分配，避免相似任务集中在同一GPU
TASKS_PER_GPU=$((total_tasks / NUM_GPUS + (total_tasks % NUM_GPUS > 0)))
echo "Each GPU will handle approximately $TASKS_PER_GPU tasks"

# 打乱任务列表，以更均匀地分配不同类型的任务
shuffled_tasks=()
# 将任务以交错方式添加到新列表
# 这样相邻的任务将分配给不同GPU，避免相似任务集中在同一GPU
for i in $(seq 0 8); do  # 9个数据集
    for j in $(seq 0 3); do  # 4个预测长度
        index=$((i + j*9))
        if [ $index -lt ${#tasks[@]} ]; then
            shuffled_tasks+=("${tasks[$index]}")
        fi
    done
done

# 添加调试输出，确认任务数量
echo "Original tasks: ${#tasks[@]}"
echo "Shuffled tasks: ${#shuffled_tasks[@]}"

# 重新赋值任务列表
tasks=("${shuffled_tasks[@]}")

# 初始化GPU状态数组
declare -a gpu_status=()
declare -a gpu_pid=()
for ((i=0; i<$NUM_GPUS; i++)); do
    gpu_status[$i]="free"
    gpu_pid[$i]=""
done

# 运行任务的函数
run_task() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    local data_path=$5
    local data_type=$6
    local enc_in=$7
    local batch_size=$8
    local learning_rate=$9
    local cycle=${10}
    local train_epochs=${11}
    local patience=${12}
    local model_type=${13}
    local d_model=${14}
    local use_revin=${15}
    
    # 确认参数均已正确传递
    echo "Parameters debug:"
    echo "  model_name: $model_name"
    echo "  pred_len: $pred_len"
    echo "  data_name: $data_name"
    echo "  model_type: $model_type"
    echo "  cycle: $cycle"
    echo "  use_revin: $use_revin"
    
    local log_file="logs/$ma_type/${model_name}_${model_type}_${data_name}_${seq_len}_${pred_len}.log"
    echo "  log_file: $log_file"
    
    echo "Starting task on GPU $gpu_id: $model_name ($model_type) on $data_name with pred_len $pred_len"
    
    # 设置GPU状态为忙
    gpu_status[$gpu_id]="busy"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 构建命令参数
    local cmd="CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
      --is_training 1 \
      --root_path ./dataset/ \
      --data_path $data_path \
      --model_id ${data_name}_${model_type}_${seq_len}_${pred_len} \
      --model $model_name \
      --data $data_type \
      --features M \
      --seq_len $seq_len \
      --pred_len $pred_len \
      --enc_in $enc_in \
      --cycle $cycle \
      --model_type $model_type \
      --train_epochs $train_epochs \
      --patience $patience \
      --batch_size $batch_size \
      --learning_rate $learning_rate \
      --des 'Exp' \
      --itr 1 \
      --ma_type $ma_type \
      --alpha $alpha \
      --beta $beta \
      --result_file $result_file"
    
    # 根据数据集添加特殊参数
    if [ "$use_revin" -eq 1 ]; then
        cmd="$cmd --use_revin 0"
    fi
    
    cmd="$cmd --gpu 0 > $log_file 2>&1"
    
    # 执行命令
    eval $cmd
    
    local exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Task on GPU $gpu_id failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Task completed successfully on GPU $gpu_id"
    fi
    
    # 任务完成，标记GPU为空闲
    gpu_status[$gpu_id]="free"
    echo "Task completed on GPU $gpu_id: $model_name ($model_type) on $data_name with pred_len $pred_len (took ${duration}s)"
}

# 显示当前进度信息
show_status() {
    echo "--------------------"
    echo "Current progress: $task_index / $total_tasks tasks assigned"
    for ((i=0; i<$NUM_GPUS; i++)); do
        echo "GPU $i: ${gpu_status[$i]}"
    done
    echo "--------------------"
}

# 初始任务分配
task_index=0
for gpu_id in $(seq 0 $((NUM_GPUS-1))); do
    if [ $task_index -lt $total_tasks ]; then
        # 解析任务，添加调试输出
        echo "Processing task: ${tasks[$task_index]}"
        IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in batch_size learning_rate cycle train_epochs patience model_type d_model use_revin <<< "${tasks[$task_index]}"
        
        echo "Task parameters:"
        echo "  model_name=$model_name, pred_len=$pred_len, data_name=$data_name"
        echo "  model_type=$model_type, cycle=$cycle"
        
        # 在后台运行任务
        run_task $gpu_id "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" "$batch_size" "$learning_rate" "$cycle" "$train_epochs" "$patience" "$model_type" "$d_model" "$use_revin" &
        gpu_pid[$gpu_id]=$!
        
        # 增加任务索引
        task_index=$((task_index + 1))
    fi
done

# 持续检查并分配新任务
while [ $task_index -lt $total_tasks ]; do
    for gpu_id in $(seq 0 $((NUM_GPUS-1))); do
        # 检查GPU是否空闲
        if [ "${gpu_status[$gpu_id]}" = "free" ]; then
            if [ $task_index -lt $total_tasks ]; then
                # 解析任务，添加调试输出
                echo "Processing task: ${tasks[$task_index]}"
                IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in batch_size learning_rate cycle train_epochs patience model_type d_model use_revin <<< "${tasks[$task_index]}"
                
                echo "Task parameters:"
                echo "  model_name=$model_name, pred_len=$pred_len, data_name=$data_name"
                echo "  model_type=$model_type, cycle=$cycle"
                
                # 在后台运行任务
                run_task $gpu_id "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" "$batch_size" "$learning_rate" "$cycle" "$train_epochs" "$patience" "$model_type" "$d_model" "$use_revin" &
                gpu_pid[$gpu_id]=$!
                
                # 增加任务索引
                task_index=$((task_index + 1))
                
                # 显示状态更新
                show_status
            else
                # 没有更多任务
                break
            fi
        fi
    done
    
    # 短暂延迟，减少CPU占用
    sleep 5
done

# 等待所有任务完成
wait
echo "All tasks completed successfully!"
