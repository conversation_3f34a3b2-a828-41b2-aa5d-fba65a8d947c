#!/bin/bash
ma_type=ema
alpha=0.3
beta=0.3
result_file="itransformer_result_last.txt"

if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/last" ]; then
    mkdir ./logs/last
fi

# 配置参数
seq_len=96
model_type='linear'

FreMLP_hidden_size=256
freMLP_embed_size=8

# 信号处理 - 添加清理函数
cleanup() {
    echo "Caught interrupt signal. Terminating all running tasks..."
    # 杀死所有子进程
    pkill -P $$
    # 给进程一点时间来优雅终止
    sleep 2
    # 如果还有子进程存活，强制终止
    pkill -9 -P $$ 2>/dev/null
    echo "All tasks terminated."
    exit 1
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 检测可用的GPU数量
NUM_GPUS=$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)
echo "Detected $NUM_GPUS available GPUs"

# 如果没有检测到GPU，默认设置为1以避免错误
if [ "$NUM_GPUS" -eq 0 ]; then
    echo "Warning: No GPUs detected, defaulting to 1"
    NUM_GPUS=1
fi

# 定义GPU ID数组，与MyModel脚本保持一致
declare -a gpu_id=("0" "1" "2" "3")
NUM_GPUS=${#gpu_id[@]}

# 创建任务队列
# declare -a model_array=("MyModel" "xPatch" "PaiFilter" "SparseTSF" "FITS" "Amplifier") 
# declare -a model_array=("CycleNet" "DLinear")
# declare -a model_array=("iTransformer")
# declare -a model_array=("FreTS")

declare -a model_array=("iTransformer")
declare -a pred_len_array=(96 192 336 720)
# declare -a data_array=("ETTh1" "ETTh2" "ETTm1" "ETTm2" "exchange" "weather" "electricity" "solar" "traffic")
declare -a data_array=("solar") 

# 创建所有任务组合
tasks=()
for pred_len in "${pred_len_array[@]}"; do
    for model_name in "${model_array[@]}"; do
        for data_name in "${data_array[@]}"; do
            tasks+=("$model_name $pred_len $data_name")
        done
    done
done

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 运行任务的函数
run_task() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    local log_file="logs/last/${model_name}_${data_name}_${seq_len}_${pred_len}.log"
    
    echo "Starting task on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 根据数据集设置适当的参数
    local data_path=""
    local enc_in=""
    local batch_size=""
    local base_learning_rate=""  # 数据集的基准学习率
    local learning_rate=""       # 根据预测长度调整后的学习率
    local data_type="custom"
    local cycle_param="--cycle 24"
    
    # 基准学习率设置
    if [[ $data_name == "weather" ]]; then
        data_path="weather.csv"
        enc_in=21
        batch_size=2048
        base_learning_rate=0.0002
        e_layers=3
        d_model=512
        d_ff=512
        
    elif [[ $data_name == "ETTh1" ]]; then
        data_path="ETTh1.csv"
        data_type="ETTh1"
        enc_in=7
        batch_size=2048
        base_learning_rate=0.0001
        e_layers=2
        
        # Parameters vary based on prediction length for ETTh1
        if [[ $pred_len -le 192 ]]; then
            d_model=256
            d_ff=256
        else
            d_model=512
            d_ff=512
        fi
        
    elif [[ $data_name == "ETTh2" ]]; then
        data_path="ETTh2.csv"
        data_type="ETTh2"
        enc_in=7
        batch_size=2048
        base_learning_rate=0.0001
        e_layers=2
        d_model=128
        d_ff=128
        
    elif [[ $data_name == "ETTm1" ]]; then
        data_path="ETTm1.csv"
        data_type="ETTm1"
        enc_in=7
        batch_size=2048
        base_learning_rate=0.0001
        e_layers=2
        d_model=128
        d_ff=128
        
    elif [[ $data_name == "ETTm2" ]]; then
        data_path="ETTm2.csv"
        data_type="ETTm2"
        enc_in=7
        batch_size=2048
        base_learning_rate=0.0001
        e_layers=2
        d_model=128
        d_ff=128

    elif [[ $data_name == "exchange" ]]; then
        data_path="exchange_rate.csv"
        enc_in=8
        batch_size=32
        base_learning_rate=0.0001
        e_layers=2
        d_model=128
        d_ff=128
    
    elif [[ $data_name == "electricity" ]]; then
        data_path="electricity.csv"
        enc_in=321
        batch_size=16
        base_learning_rate=0.0005
        e_layers=3
        d_model=512
        d_ff=512

    elif [[ $data_name == "traffic" ]]; then
        data_path="traffic.csv"
        enc_in=862
        batch_size=16
        base_learning_rate=0.001
        e_layers=4
        d_model=512
        d_ff=512
    
    
    elif [[ $data_name == "solar" ]]; then
        data_path="solar.txt"
        data_type="Solar"
        enc_in=137
        batch_size=512
        base_learning_rate=0.0005
        e_layers=2
        d_model=512
        d_ff=512
        cycle_param=""  
    fi
    
    
    # itransformer 学习率固定为0.001
    learning_rate=$base_learning_rate
    
    # 检查学习率是否为0，如果是则使用最小有效值
    if [[ "$learning_rate" == "0" || "$learning_rate" == "0.0" || "$learning_rate" == "0.00000000" ]]; then
        echo "Warning: Learning rate calculated as zero. Using minimum value instead."
        if [[ $data_name == "exchange" ]]; then
            learning_rate="0.00001"  # 对汇率数据使用极小值
        else
            learning_rate="0.0001"   # 对其他数据使用较小值
        fi
    fi
    
    # 打印调整后的学习率
    echo "Base learning rate: $base_learning_rate, Adjusted learning rate: $learning_rate"
    
    
    # 运行任务
    CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
      --is_training 1 \
      --root_path ./dataset/ \
      --data_path $data_path \
      --model_id ${model_name}_${data_name}_sl${seq_len}_pl${pred_len} \
      --model $model_name \
      --data $data_type \
      --features M \
      --seq_len $seq_len \
      --pred_len $pred_len \
      --enc_in $enc_in \
      --dec_in $enc_in \
      --c_out $enc_in \
      --des 'Exp' \
      --itr 1 \
      --batch_size $batch_size \
      --FreMLP_embed_size $freMLP_embed_size \
      --FreMLP_hidden_size $FreMLP_hidden_size \
      --learning_rate $learning_rate \
      --e_layers $e_layers \
      --d_model $d_model \
      --d_ff $d_ff \
      --result_file $result_file \
      $cycle_param \
      --model_type $model_type \
      --lradj 'sigmoid' \
      --hidden_size 512 \
      --SCI 0 \
      --ma_type $ma_type \
      --alpha $alpha \
      --beta $beta \
      --gpu 0 > $log_file 2>&1
    
    local exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Task on GPU $gpu_id failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Task completed successfully on GPU $gpu_id"
    fi
    
    echo "Task completed on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len (took ${duration}s)"
}

# 显示任务分配方式
echo "Distributing tasks across ${NUM_GPUS} GPUs in a round-robin fashion"

# 启动进程数组
pids=()

# 直接分配任务到GPU上
for ((i=0; i<$total_tasks; i++)); do
    # 计算应该使用哪个GPU
    gpu_idx=$((i % NUM_GPUS))
    current_gpu=${gpu_id[$gpu_idx]}
    
    # 解析任务
    IFS=' ' read -r model_name pred_len data_name <<< "${tasks[$i]}"
    
    # 在后台运行任务
    run_task $current_gpu "$model_name" "$pred_len" "$data_name" &
    pids+=($!)
    
    # 简单的进度显示
    echo "Started task $((i+1))/$total_tasks on GPU $current_gpu"
    
    # 如果已经分配了所有GPU的任务，稍微等待一会再继续分配
    # 这可以防止一下子启动太多进程
    if (( (i+1) % NUM_GPUS == 0 )); then
        echo "Waiting for current batch of tasks to initialize before continuing..."
        sleep 2
    fi
done

# 等待所有任务完成
echo "All tasks have been started. Waiting for completion..."
wait
echo "All tasks completed successfully!"

# # 添加ILI数据集处理部分
# echo "Starting ILI dataset tasks..."
# seq_len=36

# # ILI数据集的基准学习率
# ili_base_learning_rate=0.01

# for gpu_id in $(seq 4 $((NUM_GPUS-1))); do
#     # 等待GPU空闲
#     while [ "${gpu_status[$gpu_id]}" != "free" ]; do
#         sleep 5
#     done
    
#     for pred_len in 24 36 48 60; do
#         echo "Starting ILI task on GPU $gpu_id with pred_len $pred_len"
#         gpu_status[$gpu_id]="busy"
        
#         # 根据预测长度调整学习率
#         if [[ $pred_len -eq 24 ]]; then
#             ili_learning_rate=$ili_base_learning_rate
#         elif [[ $pred_len -eq 36 ]]; then
#             ili_learning_rate=$(awk -v base="$ili_base_learning_rate" 'BEGIN {printf "%.8f", base * 0.8}')
#         elif [[ $pred_len -eq 48 ]]; then
#             ili_learning_rate=$(awk -v base="$ili_base_learning_rate" 'BEGIN {printf "%.8f", base * 0.6}')
#         elif [[ $pred_len -eq 60 ]]; then
#             ili_learning_rate=$(awk -v base="$ili_base_learning_rate" 'BEGIN {printf "%.8f", base * 0.4}')
#         fi
        
#         # 检查学习率是否为0，如果是则使用最小有效值
#         if [[ "$ili_learning_rate" == "0" || "$ili_learning_rate" == "0.0" || "$ili_learning_rate" == "0.00000000" ]]; then
#             echo "Warning: ILI learning rate calculated as zero. Using minimum value instead."
#             ili_learning_rate="0.001"
#         fi
        
#         echo "ILI Base learning rate: $ili_base_learning_rate, Adjusted learning rate: $ili_learning_rate"
        
#         # 运行ILI任务
#         CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
#           --is_training 1 \
#           --root_path ./dataset/ \
#           --data_path national_illness.csv \
#           --model_id ili_${pred_len}_${ma_type} \
#           --model MyModel \
#           --data custom \
#           --features M \
#           --seq_len $seq_len \
#           --label_len 18 \
#           --pred_len $pred_len \
#           --enc_in 7 \
#           --des 'Exp' \
#           --itr 1 \
#           --batch_size 32 \
#           --learning_rate $ili_learning_rate \
#           --lradj 'type3' \
#           --patch_len 6 \
#           --stride 3 \
#           --ma_type $ma_type \
#           --alpha $alpha \
#           --beta $beta \
#           --FreMLP_embed_size $freMLP_embed_size \
#           --FreMLP_hidden_size $FreMLP_hidden_size \
#           --model_type $model_type \
#           --gpu 0 > logs/$ma_type/MyModel_ili_${seq_len}_${pred_len}.log 2>&1
          
#         gpu_status[$gpu_id]="free"
#         echo "ILI task completed on GPU $gpu_id with pred_len $pred_len"
#     done
# done

# echo "All ILI tasks completed successfully!"

