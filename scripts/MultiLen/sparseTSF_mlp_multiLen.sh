#!/bin/bash
ma_type=ema
alpha=0.3
beta=0.3

result_file="SparseTSF_mlp_result.txt"

if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/"$ma_type ]; then
    mkdir ./logs/$ma_type
fi

model_name=SparseTSF
# seq_len=96  # 移除固定序列长度

# 信号处理 - 添加清理函数
cleanup() {
    echo "Caught interrupt signal. Terminating all running tasks..."
    # 杀死所有子进程
    pkill -P $$
    # 给进程一点时间来优雅终止
    sleep 2
    # 如果还有子进程存活，强制终止
    pkill -9 -P $$ 2>/dev/null
    echo "All tasks terminated."
    exit 1
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 检测可用的GPU数量
NUM_GPUS=$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)
echo "Detected $NUM_GPUS available GPUs"

# 如果没有检测到GPU，默认设置为1以避免错误
if [ "$NUM_GPUS" -eq 0 ]; then
    echo "Warning: No GPUs detected, defaulting to 1"
    NUM_GPUS=1
fi

# 指定要使用的GPU
declare -a gpu_id=("0" "1" "2" "3")

# 创建任务队列
tasks=()

# ETTh1 - 使用原文MLP参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7 256 0.002 16 30 5 mlp 128")
# done

# # ETTh2 - 使用原文MLP参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len ETTh2 ETTh2.csv ETTh2 7 128 0.001 16 30 5 mlp 128")
# done

# # ETTm1 - 使用原文MLP参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len ETTm1 ETTm1.csv ETTm1 7 256 0.001 4 30 5 mlp 128")
# done

# # ETTm2 - 使用原文MLP参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len ETTm2 ETTm2.csv ETTm2 7 256 0.001 4 30 5 mlp 128")
# done


# exchange - 使用原文MLP参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len exchange exchange_rate.csv custom 8 32 0.00001 4 30 5 mlp 128")
# done

# # Weather - 使用原文MLP参数
# for pred_len in 96 192 336 720
# do
#     tasks+=("$model_name $pred_len weather weather.csv custom 21 256 0.05 4 30 5 mlp 128")
# done

# # Electricity - 使用原文MLP参数 - 按照用户要求调整顺序
# for pred_len in 720 336 192 96
# do
#     tasks+=("$model_name $pred_len electricity electricity.csv custom 321 64 0.02 16 30 5 mlp 128")
# done

# # Solar - 使用原文MLP参数
for pred_len in 96 192 336 720
do
    tasks+=("$model_name $pred_len solar solar.txt Solar 137 128 0.01 16 30 5 mlp 512")
done

# Traffic - 使用原文MLP参数 
for pred_len in 720 336 192 96
do
    tasks+=("$model_name $pred_len traffic traffic.csv custom 862 32 0.01 16 30 5 mlp 512")
done

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 添加调试输出，检查任务内容
echo "Task format check:"
echo "${tasks[0]}"

# 按照任务复杂度排序 - 使用简单的方式（不使用关联数组）
# 创建带权重的任务列表
weighted_tasks=()

# 为每个任务分配权重
for ((i=0; i<${#tasks[@]}; i++)); do
    task="${tasks[$i]}"
    
    # 提取数据集名称、输入维度和预测长度
    dataset=$(echo "$task" | awk '{print $3}')
    enc_in=$(echo "$task" | awk '{print $7}')
    pred_len=$(echo "$task" | awk '{print $2}')
    
    # 获取序列长度
    seq_len=96  # 默认值
    if [[ "$dataset" == "traffic" ]]; then
        seq_len=720
    elif [[ "$dataset" == "solar" ]]; then
        seq_len=512
    elif [[ "$dataset" == "ETTh1" ]]; then
        seq_len=336
    elif [[ "$dataset" == "ETTh2" ]]; then
        seq_len=512
    elif [[ "$dataset" == "ETTm1" ]]; then
        seq_len=336
    elif [[ "$dataset" == "ETTm2" ]]; then
        seq_len=336
    elif [[ "$dataset" == "exchange" ]]; then
        seq_len=96
    elif [[ "$dataset" == "weather" ]]; then
        seq_len=512
    elif [[ "$dataset" == "electricity" ]]; then
        seq_len=720
    fi
    
    # 计算综合内存需求评分: 特征维度 × 序列长度 × 预测长度
    task_weight=$((enc_in * seq_len * pred_len))
    
    # 添加带权重的任务
    weighted_tasks+=("$task_weight|$i|$task")
done

# 对任务按照权重排序（从大到小）
IFS=$'\n' sorted_weighted_tasks=($(echo "${weighted_tasks[*]}" | sort -nr))
unset IFS

# 添加调试输出，显示任务计算的权重值
echo "任务内存权重排序："
for weighted_task in "${sorted_weighted_tasks[@]:0:10}"; do
    weight=$(echo "$weighted_task" | cut -d'|' -f1)
    task=$(echo "$weighted_task" | cut -d'|' -f3-)
    dataset=$(echo "$task" | awk '{print $3}')
    pred_len=$(echo "$task" | awk '{print $2}')
    enc_in=$(echo "$task" | awk '{print $7}')
    echo "  $dataset (特征: $enc_in, 预测长度: $pred_len) - 权重: $weight"
done
echo "  ..."

# 使用基于累计显存的分配方式
gpu_count=${#gpu_id[@]}
gpu_total_weight=()  # 记录每个GPU累计的任务权重
gpu_tasks=()        # 用于记录每个GPU分配的任务

# 初始化计数器
for ((i=0; i<$gpu_count; i++)); do
    gpu_total_weight[$i]=0
    gpu_tasks[$i]=""
done

# 将任务分配给GPU - 基于累计内存使用
for weighted_task in "${sorted_weighted_tasks[@]}"; do
    # 提取权重和原始索引
    weight=$(echo "$weighted_task" | cut -d'|' -f1)
    idx=$(echo "$weighted_task" | cut -d'|' -f2)
    
    # 找出当前累计权重最小的GPU
    min_weight=9223372036854775807  # 最大的64位整数
    min_gpu=0
    for ((j=0; j<$gpu_count; j++)); do
        if [[ ${gpu_total_weight[$j]} -lt $min_weight ]]; then
            min_weight=${gpu_total_weight[$j]}
            min_gpu=$j
        fi
    done
    
    # 记录任务分配和累计权重
    gpu_tasks[$min_gpu]="${gpu_tasks[$min_gpu]} $idx"  # 存储索引
    gpu_total_weight[$min_gpu]=$((gpu_total_weight[$min_gpu] + weight))
done

# 构建最终的任务队列
final_tasks=()
for ((i=0; i<$gpu_count; i++)); do
    echo "GPU ${gpu_id[$i]} 分配详情:"
    echo "  累计内存权重: ${gpu_total_weight[$i]}"
    echo "  任务列表:"
    
    # 提取这个GPU的所有任务索引
    task_indices=(${gpu_tasks[$i]})
    task_count=0
    
    for idx in "${task_indices[@]}"; do
        if [[ -n "$idx" ]]; then
            task_count=$((task_count + 1))
            # 获取原始加权任务
            weighted_task="${sorted_weighted_tasks[$idx]}"
            # 提取原始任务（去掉权重和索引）
            original_idx=$(echo "$weighted_task" | cut -d'|' -f2)
            task="${tasks[$original_idx]}"
            
            # 提取并显示数据集、特征数和预测长度
            dataset=$(echo "$task" | awk '{print $3}')
            pred_len=$(echo "$task" | awk '{print $2}')
            enc_in=$(echo "$task" | awk '{print $7}')
            
            echo "    $dataset (特征: $enc_in, 预测长度: $pred_len)"
            
            # 添加到最终任务列表
            final_tasks+=("$task")
        fi
    done
    echo "  总计任务数: $task_count"
done

# 用最终平衡后的任务替换原始任务列表
tasks=("${final_tasks[@]}")

# 显示最终任务数量确认
echo "Final task count: ${#tasks[@]}"

# 初始化GPU状态数组
declare -a gpu_status=()
declare -a gpu_pid=()
for ((i=0; i<$NUM_GPUS; i++)); do
    gpu_status[$i]="free"
    gpu_pid[$i]=""
done

# 运行任务的函数
run_task() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    local data_path=$5
    local data_type=$6
    local enc_in=$7
    local batch_size=$8
    local learning_rate=$9
    local period_len=${10}
    local train_epochs=${11}
    local patience=${12}
    local model_type=${13}
    local d_model=${14}
    
    # 根据数据集设置序列长度 - 移到函数开始处设置，确保后续使用时已有值
    local seq_len=96  # 默认值
    if [[ $data_name == "traffic" ]]; then
        seq_len=720
    elif [[ $data_name == "solar" ]]; then
        seq_len=512
    elif [[ $data_name == "ETTh1" ]]; then
        seq_len=336
    elif [[ $data_name == "ETTh2" ]]; then
        seq_len=512
    elif [[ $data_name == "ETTm1" ]]; then
        seq_len=336
    elif [[ $data_name == "ETTm2" ]]; then
        seq_len=336
    elif [[ $data_name == "exchange" ]]; then
        seq_len=96
    elif [[ $data_name == "weather" ]]; then
        seq_len=512
    elif [[ $data_name == "electricity" ]]; then
        seq_len=720
    fi
    
    # 确认参数均已正确传递
    echo "Parameters debug:"
    echo "  model_name: $model_name"
    echo "  pred_len: $pred_len"
    echo "  data_name: $data_name"
    echo "  model_type: $model_type"
    echo "  d_model: $d_model"
    echo "  seq_len: $seq_len"
    
    # 确保所有变量都有值，避免空的文件名部分
    if [[ -z "$model_name" ]]; then model_name="unknown"; fi
    if [[ -z "$model_type" ]]; then model_type="unknown"; fi
    if [[ -z "$data_name" ]]; then data_name="unknown"; fi
    if [[ -z "$seq_len" ]]; then seq_len="0"; fi
    if [[ -z "$pred_len" ]]; then pred_len="0"; fi
    
    local log_file="logs/$ma_type/${model_name}_${model_type}_${data_name}_${seq_len}_${pred_len}.log"
    echo "  log_file: $log_file"
    
    echo "Starting task on GPU $gpu_id: $model_name ($model_type) on $data_name with pred_len $pred_len"
    
    # 设置GPU状态为忙
    gpu_status[$gpu_id]="busy"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 运行任务
    CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
      --is_training 1 \
      --root_path ./dataset/ \
      --data_path $data_path \
      --model_id ${data_name}_${pred_len}_${ma_type}_${model_name}_${data_type}_ftM_sl${seq_len}_ll48_pl${pred_len}_Exp_0 \
      --model $model_name \
      --data $data_type \
      --features M \
      --seq_len $seq_len \
      --pred_len $pred_len \
      --period_len $period_len \
      --model_type $model_type \
      --d_model $d_model \
      --enc_in $enc_in \
      --train_epochs $train_epochs \
      --patience $patience \
      --batch_size $batch_size \
      --learning_rate $learning_rate \
      --des 'Exp' \
      --itr 1 \
      --ma_type $ma_type \
      --alpha $alpha \
      --beta $beta \
      --result_file $result_file \
      --gpu 0 > $log_file 2>&1
    
    local exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Task on GPU $gpu_id failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Task completed successfully on GPU $gpu_id"
    fi
    
    # 任务完成，标记GPU为空闲
    gpu_status[$gpu_id]="free"
    echo "Task completed on GPU $gpu_id: $model_name ($model_type) on $data_name with pred_len $pred_len (took ${duration}s)"
}

# 显示当前进度信息
show_status() {
    echo "--------------------"
    echo "Current progress: $task_index / $total_tasks tasks assigned"
    for ((i=0; i<$NUM_GPUS; i++)); do
        echo "GPU $i: ${gpu_status[$i]}"
    done
    echo "--------------------"
}

# 初始任务分配
task_index=0
for gpu_id in "${gpu_id[@]}"; do
    if [ $task_index -lt $total_tasks ]; then
        # 解析任务，添加调试输出
        echo "Processing task: ${tasks[$task_index]}"
        # 确保任务字符串非空
        if [[ -z "${tasks[$task_index]}" ]]; then
            echo "ERROR: Empty task at index $task_index, skipping..."
            task_index=$((task_index + 1))
            continue
        fi
        
        IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in batch_size learning_rate period_len train_epochs patience model_type d_model <<< "${tasks[$task_index]}"
        
        # 验证关键参数
        if [[ -z "$model_name" || -z "$pred_len" || -z "$data_name" || -z "$model_type" ]]; then
            echo "ERROR: Missing critical parameters in task at index $task_index, skipping..."
            echo "  model_name=$model_name, pred_len=$pred_len, data_name=$data_name"
            echo "  model_type=$model_type, d_model=$d_model"
            task_index=$((task_index + 1))
            continue
        fi
        
        echo "Task parameters:"
        echo "  model_name=$model_name, pred_len=$pred_len, data_name=$data_name"
        echo "  model_type=$model_type, d_model=$d_model"
        
        # 在后台运行任务
        run_task $gpu_id "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" "$batch_size" "$learning_rate" "$period_len" "$train_epochs" "$patience" "$model_type" "$d_model" &
        gpu_pid[$gpu_id]=$!
        
        # 增加任务索引
        task_index=$((task_index + 1))
    fi
done

# 持续检查并分配新任务
while [ $task_index -lt $total_tasks ]; do
    for gpu_id in "${gpu_id[@]}"; do
        # 检查GPU是否空闲
        if [ "${gpu_status[$gpu_id]}" = "free" ]; then
            if [ $task_index -lt $total_tasks ]; then
                # 解析任务，添加调试输出
                echo "Processing task: ${tasks[$task_index]}"
                # 确保任务字符串非空
                if [[ -z "${tasks[$task_index]}" ]]; then
                    echo "ERROR: Empty task at index $task_index, skipping..."
                    task_index=$((task_index + 1))
                    continue
                fi
                
                IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in batch_size learning_rate period_len train_epochs patience model_type d_model <<< "${tasks[$task_index]}"
                
                # 验证关键参数
                if [[ -z "$model_name" || -z "$pred_len" || -z "$data_name" || -z "$model_type" ]]; then
                    echo "ERROR: Missing critical parameters in task at index $task_index, skipping..."
                    echo "  model_name=$model_name, pred_len=$pred_len, data_name=$data_name"
                    echo "  model_type=$model_type, d_model=$d_model"
                    task_index=$((task_index + 1))
                    continue
                fi
                
                echo "Task parameters:"
                echo "  model_name=$model_name, pred_len=$pred_len, data_name=$data_name"
                echo "  model_type=$model_type, d_model=$d_model"
                
                # 在后台运行任务
                run_task $gpu_id "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" "$batch_size" "$learning_rate" "$period_len" "$train_epochs" "$patience" "$model_type" "$d_model" &
                gpu_pid[$gpu_id]=$!
                
                # 增加任务索引
                task_index=$((task_index + 1))
                
                # 显示状态更新
                show_status
            else
                # 没有更多任务
                break
            fi
        fi
    done
    
    # 短暂延迟，减少CPU占用
    sleep 5
done

# 等待所有任务完成
wait
echo "All tasks completed successfully!"
