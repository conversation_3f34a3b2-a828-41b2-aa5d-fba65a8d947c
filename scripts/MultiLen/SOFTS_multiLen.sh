#!/bin/bash
ma_type=ema
alpha=0.3
beta=0.3

result_file="softs_result.txt"

if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/last" ]; then
    mkdir ./logs/last
fi

model_name=SOFTS
# seq_len=96  # 移除固定序列长度

# Original SOFTS training parameters
train_epochs=50
patience=50

# 信号处理 - 添加清理函数
cleanup() {
    echo "Caught interrupt signal. Terminating all running tasks..."
    # 杀死所有子进程
    pkill -P $$
    # 给进程一点时间来优雅终止
    sleep 2
    # 如果还有子进程存活，强制终止
    pkill -9 -P $$ 2>/dev/null
    echo "All tasks terminated."
    exit 1
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 检测可用的GPU数量
NUM_GPUS=$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)
echo "Detected $NUM_GPUS available GPUs"

# 如果没有检测到GPU，默认设置为1以避免错误
if [ "$NUM_GPUS" -eq 0 ]; then
    echo "Warning: No GPUs detected, defaulting to 1"
    NUM_GPUS=1
fi

# 指定要使用的GPU
declare -a gpu_id=("0" "1" "2" "3")
# declare -a gpu_id=("0")

# 创建任务队列
tasks=()

# 添加所有任务到队列
for pred_len in 96 192 336 720
do
    # ETTh1
    # tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7 256 256 256 1 2048 0.0005 sigmoid")
    
    # # ETTh2
    # tasks+=("$model_name $pred_len ETTh2 ETTh2.csv ETTh2 7 128 64 128 2 2048 0.0005 sigmoid")
    
    # # ETTm1
    # tasks+=("$model_name $pred_len ETTm1 ETTm1.csv ETTm1 7 128 64 128 2 2048 0.0005 sigmoid")
    
    # # ETTm2
    # tasks+=("$model_name $pred_len ETTm2 ETTm2.csv ETTm2 7 256 256 128 2 2048 0.0001 sigmoid")

    # # Exchange
    # tasks+=("$model_name $pred_len exchange exchange_rate.csv custom 8 256 128 512 2 32 0.00001 sigmoid")
    
    # # Weather
    # tasks+=("$model_name $pred_len weather weather.csv custom 21 512 128 512 3 1024 0.0005 sigmoid")
    
    # # Solar
    # tasks+=("$model_name $pred_len solar solar.txt Solar 137 512 128 512 2 256 0.005 sigmoid")

    # # Electricity
    # tasks+=("$model_name $pred_len electricity electricity.csv custom 321 512 128 512 3 48 0.005 sigmoid")
    
    # Traffic - Updated with original SOFTS parameters
    tasks+=("$model_name $pred_len traffic traffic.csv custom 862 512 128 512 3 16 0.0003 sigmoid")
done

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 运行任务的函数
run_task() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    local data_path=$5
    local data_type=$6
    local enc_in=$7
    local d_model=$8
    local d_core=$9
    local d_ff=${10}
    local e_layers=${11}
    local batch_size=${12}
    local learning_rate=${13}
    local lradj=${14:-sigmoid}  # 默认使用sigmoid
    
    # 根据数据集设置序列长度（保持原设置不变）
    local seq_len=96  # 默认值
    if [[ $data_name == "traffic" ]]; then
        seq_len=96  # Updated to match original SOFTS configuration
    elif [[ $data_name == "solar" ]]; then
        seq_len=512
    elif [[ $data_name == "ETTh1" ]]; then
        seq_len=336
    elif [[ $data_name == "ETTh2" ]]; then
        seq_len=512
    elif [[ $data_name == "ETTm1" ]]; then
        seq_len=336
    elif [[ $data_name == "ETTm2" ]]; then
        seq_len=336
    elif [[ $data_name == "exchange" ]]; then
        seq_len=96
    elif [[ $data_name == "weather" ]]; then
        seq_len=512
    elif [[ $data_name == "electricity" ]]; then
        seq_len=720
    fi
    
    local log_file="logs/last/${model_name}_${data_name}_${seq_len}_${pred_len}.log"
    
    echo "Starting task on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 设置其他参数
    local cycle_param=""
    if [[ $data_name != "solar" ]]; then
        cycle_param="--cycle 24"
    fi
    
    # 运行任务
    CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
      --is_training 1 \
      --root_path ./dataset/ \
      --data_path $data_path \
      --model_id ${model_name}_${data_name}_sl${seq_len}_pl${pred_len} \
      --model $model_name \
      --data $data_type \
      --features M \
      --seq_len $seq_len \
      --pred_len $pred_len \
      --enc_in $enc_in \
      --dec_in $enc_in \
      --c_out $enc_in \
      --d_model $d_model \
      --d_core $d_core \
      --d_ff $d_ff \
      --e_layers $e_layers \
      --use_norm 1 \
      --des 'Exp' \
      --itr 1 \
      --batch_size $batch_size \
      --learning_rate $learning_rate \
      --train_epochs $train_epochs \
      --patience $patience \
      --lradj $lradj \
      --ma_type $ma_type \
      --alpha $alpha \
      --beta $beta \
      --result_file $result_file \
      $cycle_param \
      --gpu 0 > $log_file 2>&1
    
    local exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Task on GPU $gpu_id failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Task completed successfully on GPU $gpu_id"
    fi
    
    echo "Task completed on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len (took ${duration}s)"
}

# 初始任务分配
task_index=0
for gpu_id in "${gpu_id[@]}"; do
    if [ $task_index -lt $total_tasks ]; then
        # 解析任务
        IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in d_model d_core d_ff e_layers batch_size learning_rate lradj <<< "${tasks[$task_index]}"
        
        # 在后台运行任务
        run_task $gpu_id "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" "$d_model" "$d_core" "$d_ff" "$e_layers" "$batch_size" "$learning_rate" "$lradj" &
        
        # 增加任务索引
        task_index=$((task_index + 1))
    fi
done

# 持续检查并分配新任务
while [ $task_index -lt $total_tasks ]; do
    for gpu_id in "${gpu_id[@]}"; do
        # 检查GPU进程是否已完成
        if ! ps -p ${gpu_pid[$gpu_id]} > /dev/null 2>&1; then
            if [ $task_index -lt $total_tasks ]; then
                # 解析任务
                IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in d_model d_core d_ff e_layers batch_size learning_rate lradj <<< "${tasks[$task_index]}"
                
                # 在后台运行任务
                run_task $gpu_id "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" "$d_model" "$d_core" "$d_ff" "$e_layers" "$batch_size" "$learning_rate" "$lradj" &
                gpu_pid[$gpu_id]=$!
                
                # 增加任务索引
                task_index=$((task_index + 1))
            fi
        fi
    done
    
    # 短暂延迟，减少CPU占用
    sleep 5
done

# 等待所有任务完成
wait
echo "All tasks completed successfully!"