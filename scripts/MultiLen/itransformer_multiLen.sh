#!/bin/bash
ma_type=ema
alpha=0.3
beta=0.3
result_file="itransformer_result.txt"

if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/last" ]; then
    mkdir ./logs/last
fi

# 配置参数
model_type='linear'

FreMLP_hidden_size=256
freMLP_embed_size=8

# 信号处理 - 添加清理函数
cleanup() {
    echo "Caught interrupt signal. Terminating all running tasks..."
    # 杀死所有子进程
    pkill -P $$
    # 给进程一点时间来优雅终止
    sleep 2
    # 如果还有子进程存活，强制终止
    pkill -9 -P $$ 2>/dev/null
    echo "All tasks terminated."
    exit 1
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 检测可用的GPU数量
NUM_GPUS=$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)
echo "Detected $NUM_GPUS available GPUs"

# 如果没有检测到GPU，默认设置为1以避免错误
if [ "$NUM_GPUS" -eq 0 ]; then
    echo "Warning: No GPUs detected, defaulting to 1"
    NUM_GPUS=1
fi

# 指定要使用的GPU
declare -a gpu_id=("0" "1" "2" "3")
# declare -a gpu_id=("2" "3")

# 创建任务队列
declare -a model_array=("iTransformer")
declare -a pred_len_array=(96 192 336 720)
# declare -a pred_len_array=(720)
# declare -a data_array=("traffic" "electricity" "solar" "weather" "ETTh1" "ETTh2" "ETTm1" "ETTm2" "exchange")
declare -a data_array=("ETTh1" "ETTh2" "ETTm1" "ETTm2") 

# 创建任务列表
tasks=()

# 为每个模型、数据集和预测长度组合创建任务
for data_name in "${data_array[@]}"; do
    for pred_len in "${pred_len_array[@]}"; do
        for model_name in "${model_array[@]}"; do
            tasks+=("$model_name $pred_len $data_name")
        done
    done
done

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 运行任务的函数
run_task() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    
    echo "Starting task on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 根据数据集设置序列长度
    local seq_len=96  # 默认值
    if [[ $data_name == "traffic" ]]; then
        seq_len=720
    elif [[ $data_name == "solar" ]]; then
        seq_len=512
    elif [[ $data_name == "ETTh1" ]]; then
        seq_len=336
    elif [[ $data_name == "ETTh2" ]]; then
        seq_len=512
    elif [[ $data_name == "ETTm1" ]]; then
        seq_len=336
    elif [[ $data_name == "ETTm2" ]]; then
        seq_len=336
    elif [[ $data_name == "exchange" ]]; then
        seq_len=96
    elif [[ $data_name == "weather" ]]; then
        seq_len=512
    elif [[ $data_name == "electricity" ]]; then
        seq_len=720
    fi
    
    # 更新log文件名，确保包含所有必要部分
    log_file="logs/last/${model_name}_${data_name}_${seq_len}_${pred_len}.log"
    
    # 根据数据集设置适当的参数
    local data_path=""
    local enc_in=""
    local batch_size=""
    local base_learning_rate=""  # 数据集的基准学习率
    local learning_rate=""       # 根据预测长度调整后的学习率
    local data_type="custom"
    local cycle_param="--cycle 24"
    
    # 基准学习率设置
    if [[ $data_name == "weather" ]]; then
        data_path="weather.csv"
        enc_in=21
        batch_size=2048
        base_learning_rate=0.0002
        e_layers=3
        d_model=512
        d_ff=512
        
    elif [[ $data_name == "ETTh1" ]]; then
        data_path="ETTh1.csv"
        data_type="ETTh1"
        enc_in=7
        batch_size=2048
        base_learning_rate=0.0001
        e_layers=2
        
        # Parameters vary based on prediction length for ETTh1
        if [[ $pred_len -le 192 ]]; then
            d_model=256
            d_ff=256
        else
            d_model=512
            d_ff=512
        fi
        
    elif [[ $data_name == "ETTh2" ]]; then
        data_path="ETTh2.csv"
        data_type="ETTh2"
        enc_in=7
        batch_size=2048
        base_learning_rate=0.0001
        e_layers=2
        d_model=128
        d_ff=128
        
    elif [[ $data_name == "ETTm1" ]]; then
        data_path="ETTm1.csv"
        data_type="ETTm1"
        enc_in=7
        batch_size=2048
        base_learning_rate=0.0001
        e_layers=2
        d_model=128
        d_ff=128
        
    elif [[ $data_name == "ETTm2" ]]; then
        data_path="ETTm2.csv"
        data_type="ETTm2"
        enc_in=7
        batch_size=2048
        base_learning_rate=0.0001
        e_layers=2
        d_model=128
        d_ff=128

    elif [[ $data_name == "exchange" ]]; then
        data_path="exchange_rate.csv"
        enc_in=8
        batch_size=32
        base_learning_rate=0.0001
        e_layers=2
        d_model=128
        d_ff=128
    
    elif [[ $data_name == "electricity" ]]; then
        data_path="electricity.csv"
        enc_in=321
        batch_size=16
        base_learning_rate=0.0005
        e_layers=3
        d_model=512
        d_ff=512

    elif [[ $data_name == "traffic" ]]; then
        data_path="traffic.csv"
        enc_in=862
        batch_size=16
        base_learning_rate=0.001
        e_layers=4
        d_model=512
        d_ff=512
    
    
    elif [[ $data_name == "solar" ]]; then
        data_path="solar.txt"
        data_type="Solar"
        enc_in=137
        batch_size=512
        base_learning_rate=0.0005
        e_layers=2
        d_model=512
        d_ff=512
        cycle_param=""  
    fi
    
    
    # itransformer 学习率固定为0.001
    learning_rate=$base_learning_rate
    
    # 检查学习率是否为0，如果是则使用最小有效值
    if [[ "$learning_rate" == "0" || "$learning_rate" == "0.0" || "$learning_rate" == "0.00000000" ]]; then
        echo "Warning: Learning rate calculated as zero. Using minimum value instead."
        if [[ $data_name == "exchange" ]]; then
            learning_rate="0.00001"  # 对汇率数据使用极小值
        else
            learning_rate="0.0001"   # 对其他数据使用较小值
        fi
    fi
    
    # 打印调整后的学习率
    echo "Base learning rate: $base_learning_rate, Adjusted learning rate: $learning_rate"
    
    
    # 运行任务
    CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
      --is_training 1 \
      --root_path ./dataset/ \
      --data_path $data_path \
      --model_id ${model_name}_${data_name}_sl${seq_len}_pl${pred_len} \
      --model $model_name \
      --data $data_type \
      --features M \
      --seq_len $seq_len \
      --pred_len $pred_len \
      --enc_in $enc_in \
      --dec_in $enc_in \
      --c_out $enc_in \
      --des 'Exp' \
      --itr 1 \
      --batch_size $batch_size \
      --FreMLP_embed_size $freMLP_embed_size \
      --FreMLP_hidden_size $FreMLP_hidden_size \
      --learning_rate $learning_rate \
      --e_layers $e_layers \
      --d_model $d_model \
      --d_ff $d_ff \
      --result_file $result_file \
      $cycle_param \
      --model_type $model_type \
      --lradj 'sigmoid' \
      --hidden_size 512 \
      --SCI 0 \
      --ma_type $ma_type \
      --alpha $alpha \
      --beta $beta \
      --gpu 0 > $log_file 2>&1
    
    local exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Task on GPU $gpu_id failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Task completed successfully on GPU $gpu_id"
    fi
    
    echo "Task completed on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len (took ${duration}s)"
}

# 初始任务分配
task_index=0
for gpu_id in "${gpu_id[@]}"; do
    if [ $task_index -lt $total_tasks ]; then
        # 解析任务
        IFS=' ' read -r model_name pred_len data_name <<< "${tasks[$task_index]}"
        
        # 在后台运行任务
        run_task $gpu_id "$model_name" "$pred_len" "$data_name" &
        
        # 增加任务索引
        task_index=$((task_index + 1))
    fi
done

# 持续检查并分配新任务
while [ $task_index -lt $total_tasks ]; do
    for gpu_id in "${gpu_id[@]}"; do
        # 检查GPU进程是否已完成
        if ! ps -p ${gpu_pid[$gpu_id]} > /dev/null 2>&1; then
            if [ $task_index -lt $total_tasks ]; then
                # 解析任务
                IFS=' ' read -r model_name pred_len data_name <<< "${tasks[$task_index]}"
                
                # 在后台运行任务
                run_task $gpu_id "$model_name" "$pred_len" "$data_name" &
                gpu_pid[$gpu_id]=$!
                
                # 增加任务索引
                task_index=$((task_index + 1))
            fi
        fi
    done
    
    # 短暂延迟，减少CPU占用
    sleep 5
done

# 等待所有任务完成
wait
echo "All tasks completed successfully!"