#!/bin/bash
ma_type=ema
alpha=0.3
beta=0.3
result_file="result_search.txt"

if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/"$ma_type ]; then
    mkdir ./logs/$ma_type
fi

# 配置参数
model_type='mlp' # mlp

# 信号处理 - 添加清理函数
cleanup() {
    echo "Caught interrupt signal. Terminating all running tasks..."
    # 杀死所有子进程
    pkill -P $$
    # 给进程一点时间来优雅终止
    sleep 2
    # 如果还有子进程存活，强制终止
    pkill -9 -P $$ 2>/dev/null
    echo "All tasks terminated."
    exit 1
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 检测可用的GPU数量
NUM_GPUS=$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)
echo "Detected $NUM_GPUS available GPUs"

# 如果没有检测到GPU，默认设置为1以避免错误
if [ "$NUM_GPUS" -eq 0 ]; then
    echo "Warning: No GPUs detected, defaulting to 1"
    NUM_GPUS=1
fi

# 创建任务队列
declare -a model_array=("MyModel")
# declare -a pred_len_array=(96 192 336 720)
declare -a pred_len_array=(720 336 96 192)
declare -a seq_len_array=(720 512 336)
# declare -a pred_len_array=(24 36 48 60)
# declare -a data_array=("traffic" "electricity" "solar" "weather" "ETTh1" "ETTh2" "ETTm1" "ETTm2" "exchange")
declare -a data_array=("electricity")
declare -a gpu_id=("0" "1" "2" "3")

# 创建所有任务组合
tasks=()

for data_name in "${data_array[@]}"; do
    for model_name in "${model_array[@]}"; do
        for pred_len in "${pred_len_array[@]}"; do
            for seq_len in "${seq_len_array[@]}"; do
                tasks+=("$model_name $pred_len $data_name $seq_len")
            done
        done
    done
done

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 初始化GPU状态数组
declare -a gpu_status=()
declare -a gpu_pid=()
for ((i=0; i<$NUM_GPUS; i++)); do
    gpu_status[$i]="free"
    gpu_pid[$i]=""
done

# 运行任务的函数
run_task() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    local seq_len=$5
    
    # 为每个模型创建单独的日志目录
    local model_log_dir="logs/search/${model_name}"
    if [ ! -d "$model_log_dir" ]; then
        mkdir -p "$model_log_dir"
    fi
    
    local log_file="${model_log_dir}/${data_name}_${seq_len}_${pred_len}.log"
    
    echo "Starting task on GPU $gpu_id: $model_name on $data_name with seq_len $seq_len and pred_len $pred_len"
    
    # 设置GPU状态为忙
    gpu_status[$gpu_id]="busy"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 根据数据集设置适当的参数
    local data_path=""
    local enc_in=""
    local batch_size=""
    local base_learning_rate=""  # 数据集的基准学习率
    local learning_rate=""       # 根据预测长度调整后的学习率
    local data_type="custom"
    local cycle_param="--cycle 24"
    

    if [[ $data_name == "traffic" ]]; then
        data_path="traffic.csv"
        enc_in=862
        batch_size=16
        base_learning_rate=0.001 
        FreMLP_hidden_size=256
        freMLP_embed_size=8

    elif [[ $data_name == "solar" ]]; then
        data_path="solar.txt"
        data_type="Solar"
        enc_in=137
        batch_size=128
        base_learning_rate=0.005   # 0.005
        cycle_param="" 
        FreMLP_hidden_size=256
        freMLP_embed_size=8
    
    elif [[ $data_name == "ETTh1" ]]; then
        data_path="ETTh1.csv"
        data_type="ETTh1"
        enc_in=7
        batch_size=2048
        base_learning_rate=0.0008
        FreMLP_hidden_size=128
        freMLP_embed_size=4
        
    elif [[ $data_name == "ETTh2" ]]; then
        data_path="ETTh2.csv"
        data_type="ETTh2"
        enc_in=7
        batch_size=2048
        base_learning_rate=0.02
        FreMLP_hidden_size=128
        freMLP_embed_size=4
        
    elif [[ $data_name == "ETTm1" ]]; then
        data_path="ETTm1.csv"
        data_type="ETTm1"
        enc_in=7
        batch_size=2048
        base_learning_rate=0.006
        FreMLP_hidden_size=128
        freMLP_embed_size=4

    elif [[ $data_name == "ETTm2" ]]; then
        data_path="ETTm2.csv"
        data_type="ETTm2"
        enc_in=7
        batch_size=2048
        base_learning_rate=0.0005  
        FreMLP_hidden_size=128
        freMLP_embed_size=4

    elif [[ $data_name == "exchange" ]]; then
        data_path="exchange_rate.csv"
        enc_in=8
        batch_size=32
        base_learning_rate=0.00001 
        FreMLP_hidden_size=128
        freMLP_embed_size=4
    
    elif [[ $data_name == "weather" ]]; then
        data_path="weather.csv"
        enc_in=21
        batch_size=2048
        base_learning_rate=0.004
        FreMLP_hidden_size=256
        freMLP_embed_size=8

    elif [[ $data_name == "electricity" ]]; then
        data_path="electricity.csv"
        enc_in=321
        batch_size=64
        base_learning_rate=0.002
        FreMLP_hidden_size=256
        freMLP_embed_size=8

    elif [[ $data_name == "ili" ]]; then
        data_path="national_illness.csv"
        enc_in=7
        batch_size=32
        base_learning_rate=0.05
        local_seq_len=36
        cycle_param="" 

        # ILI数据集特殊处理
        if [[ $pred_len -eq 24 ]]; then
            learning_rate=$base_learning_rate
        elif [[ $pred_len -eq 36 ]]; then
            learning_rate=$(awk -v base="$base_learning_rate" 'BEGIN {printf "%.8f", base * 0.8}')
        elif [[ $pred_len -eq 48 ]]; then
            learning_rate=$(awk -v base="$base_learning_rate" 'BEGIN {printf "%.8f", base * 0.6}')
        elif [[ $pred_len -eq 60 ]]; then
            learning_rate=$(awk -v base="$base_learning_rate" 'BEGIN {printf "%.8f", base * 0.4}')
        fi
    fi

       
    # 对其他数据集根据预测长度调整学习率
    if [[ $data_name != "ili" && $data_name != "exchange" ]]; then
        if [[ $pred_len -eq 96 ]]; then
            learning_rate=$base_learning_rate
        elif [[ $pred_len -eq 192 ]]; then
            learning_rate=$(awk -v base="$base_learning_rate" 'BEGIN {printf "%.8f", base * 0.8}')
        elif [[ $pred_len -eq 336 ]]; then
            learning_rate=$(awk -v base="$base_learning_rate" 'BEGIN {printf "%.8f", base * 0.6}')
        elif [[ $pred_len -eq 720 ]]; then
            learning_rate=$(awk -v base="$base_learning_rate" 'BEGIN {printf "%.8f", base * 0.4}')
        fi
    elif [[ $data_name == "exchange" ]]; then
        # exchange数据集使用固定的基础学习率
        learning_rate=$base_learning_rate
    fi

    # 打印调整后的学习率
    echo "Base learning rate: $base_learning_rate, Adjusted learning rate: $learning_rate"
    
    # 运行任务
    if [[ $data_name == "ili" ]]; then
        # 运行ILI任务
        CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
          --is_training 1 \
          --root_path ./dataset/ \
          --data_path $data_path \
          --model_id ${data_name}_${pred_len}_${ma_type}_${model_name}_custom_ftM_sl${local_seq_len}_ll18_pl${pred_len}_Exp_0 \
          --model $model_name \
          --data custom \
          --features M \
          --seq_len $local_seq_len \
          --label_len 18 \
          --pred_len $pred_len \
          --enc_in $enc_in \
          --des 'Exp' \
          --itr 1 \
          --batch_size $batch_size \
          --learning_rate $learning_rate \
          --lradj 'type3' \
          --patience 100 \
          --ma_type $ma_type \
          --alpha $alpha \
          --beta $beta \
          --FreMLP_embed_size 4 \
          --FreMLP_hidden_size 128 \
          --model_type $model_type \
          --result_file $result_file \
          --gpu 0 > $log_file 2>&1
    else
        # 运行任务
        CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
          --is_training 1 \
          --root_path ./dataset/ \
          --data_path $data_path \
          --model_id ${data_name}_${pred_len}_${ma_type}_${model_name}_${data_type}_ftM_sl${seq_len}_ll48_pl${pred_len}_Exp_0 \
          --model $model_name \
          --data $data_type \
          --features M \
          --seq_len $seq_len \
          --pred_len $pred_len \
          --enc_in $enc_in \
          --des 'Exp' \
          --itr 1 \
          --batch_size $batch_size \
          --FreMLP_embed_size $freMLP_embed_size \
          --FreMLP_hidden_size $FreMLP_hidden_size \
          --learning_rate $learning_rate \
          --result_file $result_file \
          $cycle_param \
          --model_type $model_type \
          --lradj 'sigmoid' \
          --hidden_size 512 \
          --SCI 0 \
          --ma_type $ma_type \
          --alpha $alpha \
          --beta $beta \
          --gpu 0 > $log_file 2>&1
    fi
    
    local exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Task on GPU $gpu_id failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Task completed successfully on GPU $gpu_id"
    fi
    
    # 任务完成，标记GPU为空闲
    gpu_status[$gpu_id]="free"
    echo "Task completed on GPU $gpu_id: $model_name on $data_name with seq_len $seq_len and pred_len $pred_len (took ${duration}s)"
}

# 显示当前进度信息
show_status() {
    echo "--------------------"
    echo "Current progress: $task_index / $total_tasks tasks assigned"
    for ((i=0; i<$NUM_GPUS; i++)); do
        echo "GPU $i: ${gpu_status[$i]}"
    done
    echo "--------------------"
}

# 初始任务分配
task_index=0
for gpu_id in "${gpu_id[@]}"; do
    if [ $task_index -lt $total_tasks ]; then
        # 解析任务
        IFS=' ' read -r model_name pred_len data_name seq_len <<< "${tasks[$task_index]}"
        
        # 在后台运行任务
        run_task $gpu_id "$model_name" "$pred_len" "$data_name" "$seq_len" &
        gpu_pid[$gpu_id]=$!
        
        # 增加任务索引
        task_index=$((task_index + 1))
    fi
done

# 持续检查并分配新任务
while [ $task_index -lt $total_tasks ]; do
    for gpu_id in "${gpu_id[@]}"; do
        # 检查GPU是否空闲
        if [ "${gpu_status[$gpu_id]}" = "free" ]; then
            if [ $task_index -lt $total_tasks ]; then
                # 解析任务
                IFS=' ' read -r model_name pred_len data_name seq_len <<< "${tasks[$task_index]}"
                
                # 在后台运行任务
                run_task $gpu_id "$model_name" "$pred_len" "$data_name" "$seq_len" &
                gpu_pid[$gpu_id]=$!
                
                # 增加任务索引
                task_index=$((task_index + 1))
                
                # 显示状态更新
                show_status
            else
                # 没有更多任务
                break
            fi
        fi
    done
    
    # 短暂延迟，减少CPU占用
    sleep 5
done

# 等待所有任务完成
wait
echo "All tasks completed successfully!"

