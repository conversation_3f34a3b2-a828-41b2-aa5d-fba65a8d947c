#!/bin/bash
ma_type=ema
alpha=0.3
beta=0.3
result_file="lr_result_search.txt"

if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/"$ma_type ]; then
    mkdir ./logs/$ma_type
fi

# 配置参数
seq_len=96
model_type='mlp' # mlp



# 信号处理 - 添加清理函数
cleanup() {
    echo "Caught interrupt signal. Terminating all running tasks..."
    # 杀死所有子进程
    pkill -P $$
    # 给进程一点时间来优雅终止
    sleep 2
    # 如果还有子进程存活，强制终止
    pkill -9 -P $$ 2>/dev/null
    # 确保所有Python进程被终止
    pkill -f "python -u run.py" 2>/dev/null
    pkill -9 -f "python -u run.py" 2>/dev/null
    echo "All tasks terminated."
    exit 1
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 检测可用的GPU数量
NUM_GPUS=$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)
echo "Detected $NUM_GPUS available GPUs"

# 如果没有检测到GPU，默认设置为1以避免错误
if [ "$NUM_GPUS" -eq 0 ]; then
    echo "Warning: No GPUs detected, defaulting to 1"
    NUM_GPUS=1
fi

# 学习率搜索范围
# declare -a lr_search_array=("0.000002" "0.000004" "0.000005" "0.000006" "0.000008" "0.00001" "0.00002" "0.00004" "0.00005" "0.00006" "0.00008" "0.0001" "0.0002")  # exchange
declare -a lr_search_array=("0.0001" "0.0002" "0.0004" "0.0005" "0.0006" "0.0008" "0.001" "0.002" "0.004" "0.005" "0.006" "0.008" "0.01" )  # ett
# declare -a lr_search_array=("0.02" "0.04" "0.05" "0.06" "0.08" "0.1" )  # ett

declare -a lr_search_array=("0.004" "0.005" "0.006" "0.008"  "0.01" "0.02" "0.04" "0.05") # weathe
# declare -a lr_search_array=("0.0004" "0.0005" "0.0006" "0.0008" "0.001" "0.002") # ecl
# declare -a lr_search_array=("0.002" "0.004" "0.005" "0.006")    # traffic
# declare -a lr_search_array=("0.0001" "0.0002" "0.0003" "0.004")    # traffic
# declare -a lr_search_array=("0.0005" "0.0006" "0.0008" "0.001")    # traffic

# declare -a lr_search_array=("0.0008" "0.001" "0.002" "0.004" "0.005")    # solar
# declare -a lr_search_array=("0.006" "0.007" "0.008" "0.01" "0.02")    # solar

declare -a model_array=("MyModel")
# declare -a pred_len_array=(96 192 336 720)
# declare -a pred_len_array=(96 192 336 720) 
declare -a pred_len_array=(96 192 336 720)
# 只使用一个数据集进行搜索
declare -a data_array=("weather")
declare -a gpu_id=("0" "1" "2" "3")

# 完整数据集列表
# declare -a data_array=("traffic" "electricity" "solar" "weather" "ETTh1" "ETTh2" "ETTm1" "ETTm2" "exchange")

# 创建结果文件
lr_result_file="learning_rate_search_results.txt"
echo "Learning Rate Search Results" >> $lr_result_file
echo "============================" >> $lr_result_file

# 创建所有任务组合
tasks=()
for model_name in "${model_array[@]}"; do
    for pred_len in "${pred_len_array[@]}"; do
        for lr in "${lr_search_array[@]}"; do
            for data_name in "${data_array[@]}"; do
                tasks+=("$model_name $pred_len $data_name $lr")
            done
        done
    done
done

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 初始化GPU状态数组
declare -a gpu_status=()
declare -a gpu_pid=()
for ((i=0; i<$NUM_GPUS; i++)); do
    gpu_status[$i]="free"
    gpu_pid[$i]=""
done

# 初始化最佳性能跟踪
declare -A best_mse
declare -A best_mae
declare -A best_lr

# 运行任务的函数
run_task() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    local learning_rate=$5
    local log_file="logs/lr_search/${model_name}_${data_name}_${seq_len}_${pred_len}_lr${learning_rate}.log"
    
    echo "Starting task on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len, lr $learning_rate"
    
    # 设置GPU状态为忙
    gpu_status[$gpu_id]="busy"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 根据数据集设置适当的参数
    local data_path=""
    local enc_in=""
    local batch_size=""
    local data_type="custom"
    local cycle_param="--cycle 24"
    
    # 数据集配置
    if [[ $data_name == "traffic" ]]; then
        data_path="traffic.csv"
        enc_in=862
        batch_size=80
        FreMLP_hidden_size=256
        freMLP_embed_size=8
        
    elif [[ $data_name == "electricity" ]]; then
        data_path="electricity.csv"
        enc_in=321
        batch_size=160
        FreMLP_hidden_size=256
        freMLP_embed_size=8
        
    elif [[ $data_name == "solar" ]]; then
        data_path="solar.txt"
        data_type="Solar"
        enc_in=137
        batch_size=512
        cycle_param=""  
        FreMLP_hidden_size=256
        freMLP_embed_size=8
        
    elif [[ $data_name == "weather" ]]; then
        data_path="weather.csv"
        enc_in=21
        batch_size=2048
        FreMLP_hidden_size=128
        freMLP_embed_size=4
        
    elif [[ $data_name == "ETTh1" ]]; then
        data_path="ETTh1.csv"
        data_type="ETTh1"
        enc_in=7
        batch_size=2048
        FreMLP_hidden_size=128
        freMLP_embed_size=4
        
    elif [[ $data_name == "ETTh2" ]]; then
        data_path="ETTh2.csv"
        data_type="ETTh2"
        enc_in=7
        batch_size=2048
        FreMLP_hidden_size=128
        freMLP_embed_size=4
        
    elif [[ $data_name == "ETTm1" ]]; then
        data_path="ETTm1.csv"
        data_type="ETTm1"
        enc_in=7
        batch_size=256
        FreMLP_hidden_size=128
        freMLP_embed_size=4
        
    elif [[ $data_name == "ETTm2" ]]; then
        data_path="ETTm2.csv"
        data_type="ETTm2"
        enc_in=7
        batch_size=256
        FreMLP_hidden_size=128
        freMLP_embed_size=4
        
    elif [[ $data_name == "exchange" ]]; then
        data_path="exchange_rate.csv"
        enc_in=8
        batch_size=32
        FreMLP_hidden_size=128
        freMLP_embed_size=4
    fi
    
    # 运行任务
    CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
      --is_training 1 \
      --root_path ./dataset/ \
      --data_path $data_path \
      --model_id ${data_name}_${pred_len}_${ma_type}_lr${learning_rate} \
      --model $model_name \
      --data $data_type \
      --features M \
      --seq_len $seq_len \
      --pred_len $pred_len \
      --enc_in $enc_in \
      --des 'LrSearch' \
      --itr 1 \
      --batch_size $batch_size \
      --FreMLP_embed_size $freMLP_embed_size \
      --FreMLP_hidden_size $FreMLP_hidden_size \
      --learning_rate $learning_rate \
      --result_file $result_file \
      $cycle_param \
      --model_type $model_type \
      --lradj 'sigmoid' \
      --hidden_size 512 \
      --SCI 0 \
      --ma_type $ma_type \
      --alpha $alpha \
      --beta $beta \
      --gpu 0 > $log_file 2>&1
    
    local exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Task on GPU $gpu_id failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Task completed successfully on GPU $gpu_id"
        
        # 提取MSE和MAE结果并记录
        local mse=$(grep -oP 'mse:\K[0-9.]+' $log_file | tail -1)
        local mae=$(grep -oP 'mae:\K[0-9.]+' $log_file | tail -1)
        
        if [ ! -z "$mse" ] && [ ! -z "$mae" ]; then
            echo "Results for ${data_name} pred_len=${pred_len} lr=${learning_rate}: MSE=$mse, MAE=$mae"
            
            # 更新最佳结果
            local key="${data_name}_${pred_len}"
            if [ -z "${best_mse[$key]}" ] || (( $(echo "$mse < ${best_mse[$key]}" | bc -l) )); then
                best_mse[$key]=$mse
                best_mae[$key]=$mae
                best_lr[$key]=$learning_rate
                
                # 记录到结果文件
                echo "New best for ${data_name} pred_len=${pred_len}: lr=${learning_rate}, MSE=$mse, MAE=$mae" >> $lr_result_file
            fi
        else
            echo "WARNING: Could not extract MSE/MAE results from log file: $log_file"
        fi
    fi
    
    # 任务完成，标记GPU为空闲
    gpu_status[$gpu_id]="free"
    echo "Task completed on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len, lr $learning_rate (took ${duration}s)"
}

# 显示当前进度信息
show_status() {
    echo "--------------------"
    echo "Current progress: $task_index / $total_tasks tasks assigned"
    for ((i=0; i<$NUM_GPUS; i++)); do
        echo "GPU $i: ${gpu_status[$i]}"
    done
    echo "--------------------"
}

# 初始任务分配
task_index=0
for gpu_idx in "${gpu_id[@]}"; do
    if [ $task_index -lt $total_tasks ]; then
        # 解析任务
        IFS=' ' read -r model_name pred_len data_name learning_rate <<< "${tasks[$task_index]}"
        
        # 在后台运行任务
        run_task $gpu_idx "$model_name" "$pred_len" "$data_name" "$learning_rate" &
        gpu_pid[$gpu_idx]=$!
        
        # 增加任务索引
        task_index=$((task_index + 1))
    fi
done

# 持续检查并分配新任务
while [ $task_index -lt $total_tasks ]; do
    for gpu_idx in "${gpu_id[@]}"; do
        # 检查GPU是否空闲
        if [ "${gpu_status[$gpu_idx]}" = "free" ]; then
            if [ $task_index -lt $total_tasks ]; then
                # 解析任务
                IFS=' ' read -r model_name pred_len data_name learning_rate <<< "${tasks[$task_index]}"
                
                # 在后台运行任务
                run_task $gpu_idx "$model_name" "$pred_len" "$data_name" "$learning_rate" &
                gpu_pid[$gpu_idx]=$!
                
                # 增加任务索引
                task_index=$((task_index + 1))
                
                # 显示状态更新
                show_status
            else
                # 没有更多任务
                break
            fi
        fi
    done
    
    # 短暂延迟，减少CPU占用
    sleep 5
done

# 等待所有任务完成
wait

# python -u process_lr_results.py

# 打印最佳学习率结果
echo -e "\n===== Best Learning Rate Results =====" | tee -a $lr_result_file
for key in "${!best_mse[@]}"; do
    IFS='_' read -r data_name pred_len <<< "$key"
    echo "Dataset: $data_name, Pred_len: $pred_len" | tee -a $lr_result_file
    echo "  Best Learning Rate: ${best_lr[$key]}" | tee -a $lr_result_file
    echo "  MSE: ${best_mse[$key]}, MAE: ${best_mae[$key]}" | tee -a $lr_result_file
    echo "" | tee -a $lr_result_file
done

echo "All tasks completed successfully!"
echo "Learning rate search results saved to $lr_result_file"


# # ili
# seq_len=36

# for model_name in MyModel
# do 
# for pred_len in 24 36 48 60
# do
#   python -u run.py \
#     --is_training 1 \
#     --root_path ./dataset/ \
#     --data_path national_illness.csv \
#     --model_id ili_$pred_len'_'$ma_type \
#     --model $model_name \
#     --data custom \
#     --features M \
#     --seq_len $seq_len \
#     --label_len 18 \
#     --pred_len $pred_len \
#     --enc_in 7 \
#     --des 'Exp' \
#     --itr 1 \
#     --batch_size 32 \
#     --learning_rate 0.01 \
#     --lradj 'type3'\
#     --patch_len 6 \
#     --stride 3 \
#     --ma_type $ma_type \
#     --alpha $alpha \
#     --beta $beta > logs/$ma_type/$model_name'_ili_'$seq_len'_'$pred_len.log
# done
# done
