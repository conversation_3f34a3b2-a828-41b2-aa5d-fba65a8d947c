#!/usr/bin/env python
import os
import sys
import argparse
import torch
import numpy as np
import pandas as pd
from tqdm import tqdm

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), ".."))

# Import all model classes
from models.Amplifier import Model as Amplifier
from models.DLinear import Model as DLinear
# Fix FilterNet import - import the specific implementations separately
from models.PaiFilter import Model as PaiFilter
from models.TexFilter import Model as TexFilter
from models.FITS import Model as FITS
from models.FreTS import Model as FreTS
from models.iTransformer import Model as iTransformer
from models.SparseTSF import Model as SparseTSF
from models.xPatch import Model as xPatch

# Add MyModel if it exists
try:
    from models.MyModel import Model as MyModel
    models_dict = {
        'Amplifier': Amplifier,
        'DLinear': DLinear,
        'PaiFilter': <PERSON><PERSON><PERSON><PERSON><PERSON>,
        'TexFilter': <PERSON>Filter,
        'FITS': FITS,
        'FreTS': FreTS,
        'iTransformer': iTransformer,
        'SparseTSF': SparseTSF,
        'xPatch': xPatch,
        'MyModel': MyModel
    }
except ImportError:
    models_dict = {
        'Amplifier': Amplifier,
        'DLinear': DLinear,
        'PaiFilter': PaiFilter,
        'TexFilter': TexFilter,
        'FITS': FITS,
        'FreTS': FreTS,
        'iTransformer': iTransformer,
        'SparseTSF': SparseTSF,
        'xPatch': xPatch
    }

# Function to count trainable parameters
def count_parameters(model):
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

# Function to measure memory usage during inference
def measure_memory_usage(model, seq_len, pred_len, enc_in, batch_size=8, device='cuda'):
    # Check if CUDA is available
    if not torch.cuda.is_available():
        return 0  # Return 0 if CUDA is not available
    
    # Move model to device
    model = model.to(device)
    
    # Create dummy input tensor with batch size 8
    x_enc = torch.randn(batch_size, seq_len, enc_in).to(device)
    x_mark_enc = torch.randn(batch_size, seq_len, 4).to(device)  # 4 is a common dimension for time features
    x_dec = torch.randn(batch_size, pred_len, enc_in).to(device)
    x_mark_dec = torch.randn(batch_size, pred_len, 4).to(device)
    
    # Clear cache and record initial memory
    torch.cuda.empty_cache()
    torch.cuda.reset_peak_memory_stats()
    torch.cuda.synchronize()
    start_memory = torch.cuda.max_memory_allocated()
    
    # Check model class name - special handling for iTransformer
    model_class_name = model.__class__.__name__
    model_name = model_class_name
    
    # Try to identify the model if it's the generic Model class
    if model_class_name == 'Model':
        if hasattr(model, 'model_name'):
            model_name = model.model_name
        elif hasattr(model, 'enc_embedding') and hasattr(model, 'projector'):
            model_name = 'iTransformer'
    
    # Run inference
    with torch.no_grad():
        try:
            if model_name == 'iTransformer':
                # iTransformer needs all 4 inputs
                model(x_enc, x_mark_enc, x_dec, x_mark_dec)
            else:
                # Most models only need x_enc
                model(x_enc)
        except Exception as e:
            print(f"Warning: Error during inference for memory measurement: {e}")
            # Try alternative approaches for failed models
            try:
                if hasattr(model, 'forward'):
                    # Get number of parameters in forward method
                    import inspect
                    forward_params = list(inspect.signature(model.forward).parameters.keys())
                    if len(forward_params) > 1:  # First param is self
                        if len(forward_params) >= 5:  # Model might need all inputs
                            model(x_enc, x_mark_enc, x_dec, x_mark_dec)
                        elif len(forward_params) >= 3:  # Model might need encoder inputs
                            model(x_enc, x_mark_enc)
                        else:
                            # Just instantiate the model on GPU for memory measurement
                            pass
            except:
                # At least we've instantiated the model on GPU
                pass
    
    # Synchronize and get peak memory
    torch.cuda.synchronize()
    end_memory = torch.cuda.max_memory_allocated()
    
    # Calculate memory used in MB
    memory_used = (end_memory - start_memory) / (1024 * 1024)
    
    # Move model back to CPU to free GPU memory
    model = model.cpu()
    torch.cuda.empty_cache()
    
    return memory_used

# Define dataset configurations
datasets = {
    'ETTh1': {'enc_in': 7, 'features': 'M'},
    'ETTh2': {'enc_in': 7, 'features': 'M'},
    'ETTm1': {'enc_in': 7, 'features': 'M'},
    'ETTm2': {'enc_in': 7, 'features': 'M'},
    'weather': {'enc_in': 21, 'features': 'M'},
    'electricity': {'enc_in': 321, 'features': 'M'},
    'traffic': {'enc_in': 862, 'features': 'M'},
    'exchange': {'enc_in': 8, 'features': 'M'},
    'solar': {'enc_in': 137, 'features': 'M'}
}

# Define sequence lengths to test
seq_lengths = [96]
pred_lengths = [96]

# Model specific configurations based on shell scripts
model_configs = {
    'Amplifier': {
        'ETTh1': {'hidden_size': 512, 'SCI': 0},
        'ETTh2': {'hidden_size': 512, 'SCI': 0},
        'ETTm1': {'hidden_size': 128, 'SCI': 0},
        'ETTm2': {'hidden_size': 128, 'SCI': 0},
        'weather': {'hidden_size': 512, 'SCI': 0},
        'electricity': {'hidden_size': 512, 'SCI': 1},
        'traffic': {'hidden_size': 512, 'SCI': 1},
        'exchange': {'hidden_size': 512, 'SCI': 0},
        'solar': {'hidden_size': 512, 'SCI': 0}
    },
    'DLinear': {
        'ETTh1': {'individual': False},
        'ETTh2': {'individual': False},
        'ETTm1': {'individual': False},
        'ETTm2': {'individual': False},
        'weather': {'individual': False},
        'electricity': {'individual': False},
        'traffic': {'individual': False},
        'exchange': {'individual': False},
        'solar': {'individual': False}
    },
    # Update FilterNet configs to be assigned to specific implementations
    'PaiFilter': {
        'ETTh1': {'hidden_size': 256},
        'ETTh2': {'hidden_size': 256},
        'ETTm1': {'hidden_size': 256},
        'ETTm2': {'hidden_size': 128},
        'weather': {'hidden_size': 256},
        'electricity': {'hidden_size': 256},
        'traffic': {'hidden_size': 256},
        'exchange': {'hidden_size': 256},
        'solar': {'hidden_size': 256}
    },
    'TexFilter': {
        'ETTh1': {'hidden_size': 128, 'embed_size': 128, 'dropout': 0},
        'ETTh2': {'hidden_size': 128, 'embed_size': 128, 'dropout': 0},
        'ETTm1': {'hidden_size': 128, 'embed_size': 128, 'dropout': 0},
        'ETTm2': {'hidden_size': 128, 'embed_size': 128, 'dropout': 0},
        'weather': {'hidden_size': 128, 'embed_size': 128, 'dropout': 0},
        'electricity': {'hidden_size': 512, 'embed_size': 512, 'dropout': 0},
        'traffic': {'hidden_size': 512, 'embed_size': 256, 'dropout': 0},
        'exchange': {'hidden_size': 128, 'embed_size': 128, 'dropout': 0},
        'solar': {'hidden_size': 512, 'embed_size': 256, 'dropout': 0}
    },
    'FITS': {
        'ETTh1': {'H_order': 7, 'train_mode': 1, 'use_individual': 0, 'base_T': 24, 'individual': False, 'cut_freq': 30},
        'ETTh2': {'H_order': 7, 'train_mode': 1, 'use_individual': 0, 'base_T': 24, 'individual': False, 'cut_freq': 30},
        'ETTm1': {'H_order': 14, 'train_mode': 1, 'use_individual': 0, 'base_T': 96, 'individual': False, 'cut_freq': 30},
        'ETTm2': {'H_order': 14, 'train_mode': 1, 'use_individual': 0, 'base_T': 96, 'individual': False, 'cut_freq': 30},
        'weather': {'H_order': 8, 'train_mode': 1, 'use_individual': 1, 'base_T': 144, 'individual': True, 'cut_freq': 30},
        'electricity': {'H_order': 8, 'train_mode': 1, 'use_individual': 0, 'base_T': 48, 'individual': False, 'cut_freq': 30},
        'traffic': {'H_order': 8, 'train_mode': 1, 'use_individual': 0, 'base_T': 48, 'individual': False, 'cut_freq': 30},
        'exchange': {'H_order': 10, 'train_mode': 1, 'use_individual': 0, 'base_T': 48, 'individual': False, 'cut_freq': 30},
        'solar': {'H_order': 8, 'train_mode': 1, 'use_individual': 1, 'base_T': 48, 'individual': True, 'cut_freq': 30}
    },
    'FreTS': {
        'ETTh1': {'channel_independence': False},
        'ETTh2': {'channel_independence': False},
        'ETTm1': {'channel_independence': False},
        'ETTm2': {'channel_independence': False},
        'weather': {'channel_independence': False},
        'electricity': {'channel_independence': False},
        'traffic': {'channel_independence': False},
        'exchange': {'channel_independence': False},
        'solar': {'channel_independence': False}
    },
    'iTransformer': {
        'ETTh1': {'e_layers': 2, 'd_model': 512, 'd_ff': 512, 'output_attention': False, 
                 'factor': 5, 'dropout': 0.05, 'embed': 'timeF', 'activation': 'gelu',
                 'freq': 'h', 'use_norm': True, 'class_strategy': 'projection', 'n_heads': 8},
        'ETTh2': {'e_layers': 2, 'd_model': 128, 'd_ff': 128, 'output_attention': False,
                 'factor': 5, 'dropout': 0.05, 'embed': 'timeF', 'activation': 'gelu',
                 'freq': 'h', 'use_norm': True, 'class_strategy': 'projection', 'n_heads': 8},
        'ETTm1': {'e_layers': 2, 'd_model': 128, 'd_ff': 128, 'output_attention': False,
                 'factor': 5, 'dropout': 0.05, 'embed': 'timeF', 'activation': 'gelu',
                 'freq': 'h', 'use_norm': True, 'class_strategy': 'projection', 'n_heads': 8},
        'ETTm2': {'e_layers': 2, 'd_model': 128, 'd_ff': 128, 'output_attention': False,
                 'factor': 5, 'dropout': 0.05, 'embed': 'timeF', 'activation': 'gelu',
                 'freq': 'h', 'use_norm': True, 'class_strategy': 'projection', 'n_heads': 8},
        'weather': {'e_layers': 3, 'd_model': 512, 'd_ff': 512, 'output_attention': False,
                   'factor': 5, 'dropout': 0.05, 'embed': 'timeF', 'activation': 'gelu',
                   'freq': 'h', 'use_norm': True, 'class_strategy': 'projection', 'n_heads': 8},
        'electricity': {'e_layers': 3, 'd_model': 512, 'd_ff': 512, 'output_attention': False,
                       'factor': 5, 'dropout': 0.05, 'embed': 'timeF', 'activation': 'gelu',
                       'freq': 'h', 'use_norm': True, 'class_strategy': 'projection', 'n_heads': 8},
        'traffic': {'e_layers': 4, 'd_model': 512, 'd_ff': 512, 'output_attention': False,
                   'factor': 5, 'dropout': 0.05, 'embed': 'timeF', 'activation': 'gelu',
                   'freq': 'h', 'use_norm': True, 'class_strategy': 'projection', 'n_heads': 8},
        'exchange': {'e_layers': 2, 'd_model': 128, 'd_ff': 128, 'output_attention': False,
                    'factor': 5, 'dropout': 0.05, 'embed': 'timeF', 'activation': 'gelu',
                    'freq': 'h', 'use_norm': True, 'class_strategy': 'projection', 'n_heads': 8},
        'solar': {'e_layers': 2, 'd_model': 512, 'd_ff': 512, 'output_attention': False,
                 'factor': 5, 'dropout': 0.05, 'embed': 'timeF', 'activation': 'gelu',
                 'freq': 'h', 'use_norm': True, 'class_strategy': 'projection', 'n_heads': 8}
    },
    'SparseTSF': {
        'ETTh1': {'model_type': 'mlp', 'd_model': 128, 'period_len': 16},
        'ETTh2': {'model_type': 'mlp', 'd_model': 128, 'period_len': 16},
        'ETTm1': {'model_type': 'mlp', 'd_model': 128, 'period_len': 4},
        'ETTm2': {'model_type': 'mlp', 'd_model': 128, 'period_len': 4},
        'weather': {'model_type': 'mlp', 'd_model': 128, 'period_len': 4},
        'electricity': {'model_type': 'mlp', 'd_model': 128, 'period_len': 16},
        'traffic': {'model_type': 'mlp', 'd_model': 512, 'period_len': 16},
        'exchange': {'model_type': 'mlp', 'd_model': 128, 'period_len': 4},
        'solar': {'model_type': 'mlp', 'd_model': 512, 'period_len': 16}
    },
    'xPatch': {
        'ETTh1': {'patch_len': 16, 'stride': 8, 'padding_patch': 'end', 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3},
        'ETTh2': {'patch_len': 16, 'stride': 8, 'padding_patch': 'end', 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3},
        'ETTm1': {'patch_len': 16, 'stride': 8, 'padding_patch': 'end', 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3},
        'ETTm2': {'patch_len': 16, 'stride': 8, 'padding_patch': 'end', 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3},
        'weather': {'patch_len': 16, 'stride': 8, 'padding_patch': 'end', 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3},
        'electricity': {'patch_len': 16, 'stride': 8, 'padding_patch': 'end', 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3},
        'traffic': {'patch_len': 16, 'stride': 8, 'padding_patch': 'end', 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3},
        'exchange': {'patch_len': 16, 'stride': 8, 'padding_patch': 'end', 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3},
        'solar': {'patch_len': 16, 'stride': 8, 'padding_patch': 'end', 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3}
    }
}

# Try to add MyModel configuration if it exists
try:
    model_configs['MyModel'] = {
        'ETTh1': {'model_type': 'mlp', 'FreMLP_hidden_size': 128, 'FreMLP_embed_size': 4, 'individual': False, 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3},
        'ETTh2': {'model_type': 'mlp', 'FreMLP_hidden_size': 128, 'FreMLP_embed_size': 4, 'individual': False, 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3},
        'ETTm1': {'model_type': 'mlp', 'FreMLP_hidden_size': 128, 'FreMLP_embed_size': 4, 'individual': False, 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3},
        'ETTm2': {'model_type': 'mlp', 'FreMLP_hidden_size': 128, 'FreMLP_embed_size': 4, 'individual': False, 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3},
        'weather': {'model_type': 'mlp', 'FreMLP_hidden_size': 256, 'FreMLP_embed_size': 8, 'individual': False, 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3},
        'electricity': {'model_type': 'mlp', 'FreMLP_hidden_size': 256, 'FreMLP_embed_size': 8, 'individual': False, 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3},
        'traffic': {'model_type': 'mlp', 'FreMLP_hidden_size': 256, 'FreMLP_embed_size': 8, 'individual': False, 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3},
        'exchange': {'model_type': 'mlp', 'FreMLP_hidden_size': 128, 'FreMLP_embed_size': 4, 'individual': False, 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3},
        'solar': {'model_type': 'mlp', 'FreMLP_hidden_size': 256, 'FreMLP_embed_size': 8, 'individual': False, 'revin': True, 'ma_type': 'ema', 'alpha': 0.3, 'beta': 0.3}
    }
except:
    pass

def main():
    # Check if CUDA is available
    cuda_available = torch.cuda.is_available()
    device = 'cuda' if cuda_available else 'cpu'
    
    if not cuda_available:
        print("Warning: CUDA is not available. Memory usage cannot be measured.")
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Count model parameters and measure memory usage')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size for memory usage test')
    args = parser.parse_args()
    
    # Initialize results dataframe
    results = []
    
    # Loop through all combinations
    for model_name in tqdm(models_dict.keys(), desc="Models"):
        model_class = models_dict[model_name]
        
        for dataset_name, dataset_config in tqdm(datasets.items(), desc=f"Datasets for {model_name}", leave=False):
            enc_in = dataset_config['enc_in']
            
            for seq_len in seq_lengths:
                for pred_len in pred_lengths:
                    # Get model-specific configs
                    model_specific_config = model_configs.get(model_name, {}).get(dataset_name, {})
                    
                    # Common parameters for all models
                    config = {
                        'enc_in': enc_in,
                        'seq_len': seq_len,
                        'pred_len': pred_len,
                        'label_len': 48,  # Common value in most scripts
                        'c_out': enc_in,  # c_out is typically same as enc_in
                        'dec_in': enc_in,  # dec_in is typically same as enc_in
                    }
                    
                    # Update with model-specific parameters
                    config.update(model_specific_config)
                    
                    # Try to create the model and count parameters
                    try:
                        # Create model instance based on model name
                        model = model_class(configs=argparse.Namespace(**config))
                        
                        # Count parameters
                        params = count_parameters(model)
                        
                        # Measure memory usage during inference if CUDA is available
                        memory_usage = 0
                        if cuda_available:
                            try:
                                memory_usage = measure_memory_usage(
                                    model, 
                                    seq_len, 
                                    pred_len, 
                                    enc_in, 
                                    batch_size=args.batch_size,
                                    device=device
                                )
                            except Exception as e:
                                print(f"Error measuring memory for {model_name} on {dataset_name}: {e}")
                                memory_usage = 0
                        
                        # Store result
                        results.append({
                            'Model': model_name,
                            'Dataset': dataset_name,
                            'Input Length': seq_len,
                            'Output Length': pred_len,
                            'Trainable Parameters': params,
                            'GPU Memory (MB)': memory_usage
                        })
                    except Exception as e:
                        print(f"Error with {model_name} on {dataset_name} with seq_len={seq_len}, pred_len={pred_len}: {e}")
                        # Print the current config for debugging
                        print(f"Config: {config}")
    
    # Convert results to DataFrame
    df = pd.DataFrame(results)
    
    if len(df) == 0:
        print("No successful model runs. Cannot generate reports.")
        return
    
    # Print table to console
    print("\nTrainable Parameters and GPU Memory Usage for Different Models, Datasets, and Sequence Lengths:")
    print(df.to_string(index=False))
    
    # Save results to CSV
    output_file = 'model_parameter_counts.csv'
    df.to_csv(output_file, index=False)
    print(f"\nResults saved to {output_file}")
    
    # Create a pivot table for parameters
    pivot_params_df = df.pivot_table(
        index=['Model', 'Dataset'], 
        columns=['Input Length', 'Output Length'],
        values='Trainable Parameters'
    )
    
    # Create a pivot table for memory usage
    pivot_memory_df = df.pivot_table(
        index=['Model', 'Dataset'], 
        columns=['Input Length', 'Output Length'],
        values='GPU Memory (MB)'
    )
    
    # Print the pivot tables
    print("\nPivot Table of Trainable Parameters:")
    print(pivot_params_df)
    
    print("\nPivot Table of GPU Memory Usage (MB):")
    print(pivot_memory_df)
    
    # Save pivot tables to CSV
    pivot_params_output_file = 'model_parameter_counts_pivot.csv'
    pivot_params_df.to_csv(pivot_params_output_file)
    print(f"Pivot table for parameters saved to {pivot_params_output_file}")
    
    pivot_memory_output_file = 'model_memory_usage_pivot.csv'
    pivot_memory_df.to_csv(pivot_memory_output_file)
    print(f"Pivot table for memory usage saved to {pivot_memory_output_file}")

if __name__ == "__main__":
    main() 