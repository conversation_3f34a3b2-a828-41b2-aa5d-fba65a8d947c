#!/bin/bash
ma_type=ema
alpha=0.3
beta=0.3

result_file="itransformer_test_results.txt"

if [ ! -d "./test_plots" ]; then
    mkdir ./test_plots
fi

# 配置参数
seq_len=96
model_type='linear'

FreMLP_hidden_size=256
freMLP_embed_size=8

# 检测可用的GPU数量
NUM_GPUS=$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)
echo "Detected $NUM_GPUS available GPUs"

# 如果没有检测到GPU，默认设置为1以避免错误
if [ "$NUM_GPUS" -eq 0 ]; then
    echo "Warning: No GPUs detected, defaulting to 1"
    NUM_GPUS=1
fi

# 创建任务队列
declare -a model_array=("iTransformer")
declare -a pred_len_array=(96 192 336 720)
declare -a data_array=("ETTh1" "ETTh2" "ETTm1" "ETTm2" "exchange" "weather" "electricity" "solar" "traffic")

# 创建所有任务组合
tasks=()
for pred_len in "${pred_len_array[@]}"; do
    for model_name in "${model_array[@]}"; do
        for data_name in "${data_array[@]}"; do
            tasks+=("$model_name $pred_len $data_name")
        done
    done
done

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 初始化GPU状态数组
declare -a gpu_status=()
for ((i=0; i<$NUM_GPUS; i++)); do
    gpu_status[$i]="free"
done

# 运行测试任务
run_test() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    
    # 设置输出文件
    local log_file="test_plots/${model_name}_${data_name}_${seq_len}_${pred_len}_test.log"
    
    echo "Testing model on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len"
    
    # 设置GPU状态为忙
    gpu_status[$gpu_id]="busy"
    
    # 根据数据集设置适当的参数
    local data_path=""
    local enc_in=""
    local data_type="custom"
    local cycle_param="--cycle 24"
    local e_layers=3
    local d_model=512
    local d_ff=512
    
    # 基准学习率设置
    if [[ $data_name == "weather" ]]; then
        data_path="weather.csv"
        enc_in=21
        e_layers=3
        d_model=512
        d_ff=512
        
    elif [[ $data_name == "ETTh1" ]]; then
        data_path="ETTh1.csv"
        data_type="ETTh1"
        enc_in=7
        e_layers=2
        
        # Parameters vary based on prediction length for ETTh1
        if [[ $pred_len -le 192 ]]; then
            d_model=256
            d_ff=256
        else
            d_model=512
            d_ff=512
        fi
        
    elif [[ $data_name == "ETTh2" ]]; then
        data_path="ETTh2.csv"
        data_type="ETTh2"
        enc_in=7
        e_layers=2
        d_model=128
        d_ff=128
        
    elif [[ $data_name == "ETTm1" ]]; then
        data_path="ETTm1.csv"
        data_type="ETTm1"
        enc_in=7
        e_layers=2
        d_model=128
        d_ff=128
        
    elif [[ $data_name == "ETTm2" ]]; then
        data_path="ETTm2.csv"
        data_type="ETTm2"
        enc_in=7
        e_layers=2
        d_model=128
        d_ff=128

    elif [[ $data_name == "exchange" ]]; then
        data_path="exchange_rate.csv"
        enc_in=8
        e_layers=2
        d_model=128
        d_ff=128
    
    elif [[ $data_name == "electricity" ]]; then
        data_path="electricity.csv"
        enc_in=321
        e_layers=3
        d_model=512
        d_ff=512

    elif [[ $data_name == "traffic" ]]; then
        data_path="traffic.csv"
        enc_in=862
        e_layers=4
        d_model=512
        d_ff=512
    
    elif [[ $data_name == "solar" ]]; then
        data_path="solar.txt"
        data_type="Solar"
        enc_in=137
        e_layers=2
        d_model=512
        d_ff=512
        cycle_param=""  
    fi
    
    # 构建模型ID，与训练时一致
    local model_id="${data_name}_${pred_len}_${ma_type}"
    
    # 运行测试命令
    CUDA_VISIBLE_DEVICES=$gpu_id python run_test.py \
      --model_id $model_id \
      --root_path ./dataset/ \
      --data_path $data_path \
      --model $model_name \
      --data $data_type \
      --features M \
      --seq_len $seq_len \
      --pred_len $pred_len \
      --enc_in $enc_in \
      --dec_in $enc_in \
      --c_out $enc_in \
      --FreMLP_embed_size $freMLP_embed_size \
      --FreMLP_hidden_size $FreMLP_hidden_size \
      --e_layers $e_layers \
      --d_model $d_model \
      --d_ff $d_ff \
      --result_file $result_file \
      $cycle_param \
      --model_type $model_type \
      --lradj 'sigmoid' \
      --hidden_size 512 \
      --SCI 0 \
      --ma_type $ma_type \
      --alpha $alpha \
      --beta $beta \
      --revin 1 \
      --plot_result \
      --save_prediction \
      --gpu $gpu_id > $log_file 2>&1
    
    local exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Test on GPU $gpu_id failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Test completed successfully on GPU $gpu_id"
    fi
    
    # 任务完成，标记GPU为空闲
    gpu_status[$gpu_id]="free"
    echo "Test completed on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len"
}

# 显示当前进度信息
show_status() {
    echo "--------------------"
    echo "Current progress: $task_index / $total_tasks tasks assigned"
    for ((i=0; i<$NUM_GPUS; i++)); do
        echo "GPU $i: ${gpu_status[$i]}"
    done
    echo "--------------------"
}

# 初始任务分配
task_index=0
for gpu_id in $(seq 0 $((NUM_GPUS-1))); do
    if [ $task_index -lt $total_tasks ]; then
        # 解析任务
        IFS=' ' read -r model_name pred_len data_name <<< "${tasks[$task_index]}"
        
        # 在后台运行任务
        run_test $gpu_id "$model_name" "$pred_len" "$data_name" &
        
        # 增加任务索引
        task_index=$((task_index + 1))
    fi
done

# 持续检查并分配新任务
while [ $task_index -lt $total_tasks ]; do
    for gpu_id in $(seq 0 $((NUM_GPUS-1))); do
        # 检查GPU是否空闲
        if [ "${gpu_status[$gpu_id]}" = "free" ]; then
            if [ $task_index -lt $total_tasks ]; then
                # 解析任务
                IFS=' ' read -r model_name pred_len data_name <<< "${tasks[$task_index]}"
                
                # 在后台运行任务
                run_test $gpu_id "$model_name" "$pred_len" "$data_name" &
                
                # 增加任务索引
                task_index=$((task_index + 1))
                
                # 显示状态更新
                show_status
            else
                # 没有更多任务
                break
            fi
        fi
    done
    
    # 短暂延迟，减少CPU占用
    sleep 5
done

# 等待所有任务完成
wait
echo "All tests completed successfully!" 