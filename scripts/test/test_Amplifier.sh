#!/bin/bash
ma_type=ema
alpha=0.3
beta=0.3

result_file="Amplifier_test_results.txt"

if [ ! -d "./test_plots" ]; then
    mkdir ./test_plots
fi

model_name=Amplifier
seq_len=96

# 检测可用的GPU数量
NUM_GPUS=$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)
echo "Detected $NUM_GPUS available GPUs"

# 如果没有检测到GPU，默认设置为1以避免错误
if [ "$NUM_GPUS" -eq 0 ]; then
    echo "Warning: No GPUs detected, defaulting to 1"
    NUM_GPUS=1
fi

# 创建任务队列
tasks=()

# 添加所有任务到队列
for pred_len in 96 192 336 720
do
    # ETTh1 - 使用原文提供的特定参数
    if [ "$pred_len" -eq 96 ]; then
        tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7 64 0")
    elif [ "$pred_len" -eq 192 ]; then
        tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7 512 0")
    elif [ "$pred_len" -eq 336 ] || [ "$pred_len" -eq 720 ]; then
        tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7 512 0")
    fi
    
    # ETTh2 - 使用与ETTh1相同的参数
    if [ "$pred_len" -eq 96 ]; then
        tasks+=("$model_name $pred_len ETTh2 ETTh2.csv ETTh2 7 64 0")
    elif [ "$pred_len" -eq 192 ]; then
        tasks+=("$model_name $pred_len ETTh2 ETTh2.csv ETTh2 7 512 0")
    elif [ "$pred_len" -eq 336 ] || [ "$pred_len" -eq 720 ]; then
        tasks+=("$model_name $pred_len ETTh2 ETTh2.csv ETTh2 7 512 0")
    fi
    
    # ETTm1 - 使用原文提供的特定参数
    if [ "$pred_len" -eq 96 ] || [ "$pred_len" -eq 192 ] || [ "$pred_len" -eq 336 ]; then
        tasks+=("$model_name $pred_len ETTm1 ETTm1.csv ETTm1 7 128 0")
    elif [ "$pred_len" -eq 720 ]; then
        tasks+=("$model_name $pred_len ETTm1 ETTm1.csv ETTm1 7 128 0")
    fi
    
    # ETTm2 - 使用与ETTm1相同的参数
    if [ "$pred_len" -eq 96 ] || [ "$pred_len" -eq 192 ] || [ "$pred_len" -eq 336 ]; then
        tasks+=("$model_name $pred_len ETTm2 ETTm2.csv ETTm2 7 128 0")
    elif [ "$pred_len" -eq 720 ]; then
        tasks+=("$model_name $pred_len ETTm2 ETTm2.csv ETTm2 7 128 0")
    fi
    
    # Exchange
    tasks+=("$model_name $pred_len exchange exchange_rate.csv custom 8 512 0")
    
    # Electricity - 使用原文提供的特定参数
    if [ "$pred_len" -eq 96 ]; then
        tasks+=("$model_name $pred_len electricity electricity.csv custom 321 512 1")
    elif [ "$pred_len" -eq 192 ]; then
        tasks+=("$model_name $pred_len electricity electricity.csv custom 321 512 1")
    elif [ "$pred_len" -eq 336 ] || [ "$pred_len" -eq 720 ]; then
        tasks+=("$model_name $pred_len electricity electricity.csv custom 321 1024 1")
    fi
    
    # Solar
    tasks+=("$model_name $pred_len solar solar.txt Solar 137 512 0")

    # Weather
    tasks+=("$model_name $pred_len weather weather.csv custom 21 512 0")
    
    # Traffic
    tasks+=("$model_name $pred_len traffic traffic.csv custom 862 512 0")
done

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 初始化GPU状态数组
declare -a gpu_status=()
for ((i=0; i<$NUM_GPUS; i++)); do
    gpu_status[$i]="free"
done

# 运行测试任务
run_test() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    local data_path=$5
    local data_type=$6
    local enc_in=$7
    local hidden_size=$8
    local sci_flag=$9
    
    # 设置输出文件
    local log_file="test_plots/${model_name}_${data_name}_${seq_len}_${pred_len}_test.log"
    
    echo "Testing model on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len"
    
    # 设置GPU状态为忙
    gpu_status[$gpu_id]="busy"
    
    # 构建模型ID，与训练时一致
    local model_id="${data_name}_${pred_len}_${ma_type}"
    
    # 设置其他参数
    local cycle_param=""
    if [[ $data_name != "solar" ]]; then
        cycle_param="--cycle 24"
    fi
    
    # 为特定数据集使用特定设置
    if [[ $data_name == "electricity" ]]; then
        # 运行测试命令 - 使用原文的电力数据集命令参数
        CUDA_VISIBLE_DEVICES=$gpu_id python run_test.py \
          --model_id $model_id \
          --root_path ./dataset/electricity/ \
          --data_path $data_path \
          --model $model_name \
          --data $data_type \
          --features M \
          --seq_len $seq_len \
          --label_len 48 \
          --pred_len $pred_len \
          --enc_in $enc_in \
          --hidden_size $hidden_size \
          --SCI $sci_flag \
          --ma_type $ma_type \
          --alpha $alpha \
          --beta $beta \
          --revin 1 \
          --result_file $result_file \
          --plot_result \
          --save_prediction \
          --gpu $gpu_id > $log_file 2>&1
    elif [[ $data_name == "ETTh1" ]]; then
        # 运行测试命令 - 使用原文的ETTh1数据集命令参数
        CUDA_VISIBLE_DEVICES=$gpu_id python run_test.py \
          --model_id $model_id \
          --root_path ./dataset/ETT-small/ \
          --data_path $data_path \
          --model $model_name \
          --data $data_type \
          --features M \
          --seq_len $seq_len \
          --label_len 48 \
          --pred_len $pred_len \
          --enc_in $enc_in \
          --hidden_size $hidden_size \
          --SCI $sci_flag \
          --ma_type $ma_type \
          --alpha $alpha \
          --beta $beta \
          --revin 1 \
          --result_file $result_file \
          --plot_result \
          --save_prediction \
          --gpu $gpu_id > $log_file 2>&1
    elif [[ $data_name == "ETTh2" ]]; then
        # 运行测试命令 - 使用与ETTh1相同的参数
        CUDA_VISIBLE_DEVICES=$gpu_id python run_test.py \
          --model_id $model_id \
          --root_path ./dataset/ETT-small/ \
          --data_path $data_path \
          --model $model_name \
          --data $data_type \
          --features M \
          --seq_len $seq_len \
          --label_len 48 \
          --pred_len $pred_len \
          --enc_in $enc_in \
          --hidden_size $hidden_size \
          --SCI $sci_flag \
          --ma_type $ma_type \
          --alpha $alpha \
          --beta $beta \
          --revin 1 \
          --result_file $result_file \
          --plot_result \
          --save_prediction \
          --gpu $gpu_id > $log_file 2>&1
    elif [[ $data_name == "ETTm1" ]]; then
        # 运行测试命令 - 使用原文的ETTm1数据集命令参数
        CUDA_VISIBLE_DEVICES=$gpu_id python run_test.py \
          --model_id $model_id \
          --root_path ./dataset/ETT-small/ \
          --data_path $data_path \
          --model $model_name \
          --data $data_type \
          --features M \
          --seq_len $seq_len \
          --label_len 48 \
          --pred_len $pred_len \
          --enc_in $enc_in \
          --hidden_size $hidden_size \
          --SCI $sci_flag \
          --ma_type $ma_type \
          --alpha $alpha \
          --beta $beta \
          --revin 1 \
          --result_file $result_file \
          --plot_result \
          --save_prediction \
          --gpu $gpu_id > $log_file 2>&1
    elif [[ $data_name == "ETTm2" ]]; then
        # 运行测试命令 - 使用与ETTm1相同的参数
        CUDA_VISIBLE_DEVICES=$gpu_id python run_test.py \
          --model_id $model_id \
          --root_path ./dataset/ETT-small/ \
          --data_path $data_path \
          --model $model_name \
          --data $data_type \
          --features M \
          --seq_len $seq_len \
          --label_len 48 \
          --pred_len $pred_len \
          --enc_in $enc_in \
          --hidden_size $hidden_size \
          --SCI $sci_flag \
          --ma_type $ma_type \
          --alpha $alpha \
          --beta $beta \
          --revin 1 \
          --result_file $result_file \
          --plot_result \
          --save_prediction \
          --gpu $gpu_id > $log_file 2>&1
    else
        # 其他数据集使用原有的命令参数
        CUDA_VISIBLE_DEVICES=$gpu_id python run_test.py \
          --model_id $model_id \
          --root_path ./dataset/ \
          --data_path $data_path \
          --model $model_name \
          --data $data_type \
          --features M \
          --seq_len $seq_len \
          --pred_len $pred_len \
          --enc_in $enc_in \
          --hidden_size $hidden_size \
          --SCI $sci_flag \
          --ma_type $ma_type \
          --alpha $alpha \
          --beta $beta \
          --revin 1 \
          --result_file $result_file \
          $cycle_param \
          --plot_result \
          --save_prediction \
          --gpu $gpu_id > $log_file 2>&1
    fi
    
    local exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Test on GPU $gpu_id failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Test completed successfully on GPU $gpu_id"
    fi
    
    # 任务完成，标记GPU为空闲
    gpu_status[$gpu_id]="free"
    echo "Test completed on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len"
}

# 显示当前进度信息
show_status() {
    echo "--------------------"
    echo "Current progress: $task_index / $total_tasks tasks assigned"
    for ((i=0; i<$NUM_GPUS; i++)); do
        echo "GPU $i: ${gpu_status[$i]}"
    done
    echo "--------------------"
}

# 初始任务分配
task_index=0
for gpu_id in $(seq 0 $((NUM_GPUS-1))); do
    if [ $task_index -lt $total_tasks ]; then
        # 解析任务
        IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in hidden_size sci_flag <<< "${tasks[$task_index]}"
        
        # 在后台运行任务
        run_test $gpu_id "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" "$hidden_size" "$sci_flag" &
        
        # 增加任务索引
        task_index=$((task_index + 1))
    fi
done

# 持续检查并分配新任务
while [ $task_index -lt $total_tasks ]; do
    for gpu_id in $(seq 0 $((NUM_GPUS-1))); do
        # 检查GPU是否空闲
        if [ "${gpu_status[$gpu_id]}" = "free" ]; then
            if [ $task_index -lt $total_tasks ]; then
                # 解析任务
                IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in hidden_size sci_flag <<< "${tasks[$task_index]}"
                
                # 在后台运行任务
                run_test $gpu_id "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" "$hidden_size" "$sci_flag" &
                
                # 增加任务索引
                task_index=$((task_index + 1))
                
                # 显示状态更新
                show_status
            else
                # 没有更多任务
                break
            fi
        fi
    done
    
    # 短暂延迟，减少CPU占用
    sleep 5
done

# 等待所有任务完成
wait
echo "All tests completed successfully!" 