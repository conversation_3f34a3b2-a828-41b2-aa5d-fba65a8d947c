#!/bin/bash

# 确保在项目根目录下执行
cd "$(dirname "$0")/../.." || { echo "无法切换到项目根目录"; exit 1; }
PROJECT_ROOT=$(pwd)
echo "当前工作目录：$PROJECT_ROOT"

ma_type=ema
alpha=0.3
beta=0.3

result_file="mymodel_test_results.txt"

if [ ! -d "./test_plots" ]; then
    mkdir ./test_plots
fi

# 配置参数
model_name=MyModel
seq_len=96
model_type='mlp' # mlp

# 检测可用的GPU
NUM_GPUS=$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)
echo "检测到 $NUM_GPUS 个可用GPU"

# 如果没有检测到GPU，默认设置为1以避免错误
if [ "$NUM_GPUS" -eq 0 ]; then
    echo "警告: 未检测到GPU，默认设置为1"
    NUM_GPUS=1
fi

# 确保test_model.py存在
if [ ! -f "$PROJECT_ROOT/test_model.py" ]; then
    echo "错误: 在 $PROJECT_ROOT 中找不到 test_model.py"
    echo "请先确保已创建 test_model.py 文件"
    exit 1
fi
    
# 创建任务队列
# declare -a data_array=("traffic" "electricity" "solar" "weather" "ETTh1" "ETTh2" "ETTm1" "ETTm2" "exchange")
declare -a data_array=("traffic")
declare -a pred_len_array=(96 192 336 720)
declare -a gpu_id=("0" "1" "2" "3")


# 创建所有任务组合
tasks=()

for data_name in "${data_array[@]}"; do
    for pred_len in "${pred_len_array[@]}"; do
        tasks+=("$model_name $pred_len $data_name")
    done
done

# 查看总任务数
total_tasks=${#tasks[@]}
echo "总任务数: $total_tasks"

# 初始化GPU状态数组
declare -a gpu_status=()
for ((i=0; i<$NUM_GPUS; i++)); do
    gpu_status[$i]="free"
done

# 运行测试任务
run_test() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    
    # 设置输出文件
    local log_file="$PROJECT_ROOT/test_plots/${model_name}_${data_name}_${seq_len}_${pred_len}_test.log"
    
    echo "在GPU $gpu_id 上测试模型: $model_name 数据集: $data_name 预测长度: $pred_len"
    
    # 设置GPU状态为忙
    gpu_status[$gpu_id]="busy"
    
    # 根据数据集设置适当的参数
    local data_path=""
    local enc_in=""
    local data_type="custom"
    local cycle_param="--cycle 24"
    local FreMLP_hidden_size=256
    local freMLP_embed_size=8
    
    if [[ $data_name == "traffic" ]]; then
        data_path="traffic.csv"
        enc_in=862
        FreMLP_hidden_size=256
        freMLP_embed_size=8
    elif [[ $data_name == "solar" ]]; then
        data_path="solar.txt"
        data_type="Solar"
        enc_in=137
        cycle_param=""
        FreMLP_hidden_size=256
        freMLP_embed_size=8
    elif [[ $data_name == "ETTh1" ]]; then
        data_path="ETTh1.csv"
        data_type="ETTh1"
        enc_in=7
        FreMLP_hidden_size=128
        freMLP_embed_size=4
    elif [[ $data_name == "ETTh2" ]]; then
        data_path="ETTh2.csv"
        data_type="ETTh2"
        enc_in=7
        FreMLP_hidden_size=128
        freMLP_embed_size=4
    elif [[ $data_name == "ETTm1" ]]; then
        data_path="ETTm1.csv"
        data_type="ETTm1"
        enc_in=7
        FreMLP_hidden_size=128
        freMLP_embed_size=4
    elif [[ $data_name == "ETTm2" ]]; then
        data_path="ETTm2.csv"
        data_type="ETTm2"
        enc_in=7
        FreMLP_hidden_size=128
        freMLP_embed_size=4
    elif [[ $data_name == "exchange" ]]; then
        data_path="exchange_rate.csv"
        enc_in=8
        FreMLP_hidden_size=128
        freMLP_embed_size=4
    elif [[ $data_name == "weather" ]]; then
        data_path="weather.csv"
        enc_in=21
        FreMLP_hidden_size=256
        freMLP_embed_size=8
    elif [[ $data_name == "electricity" ]]; then
        data_path="electricity.csv"
        enc_in=321
        FreMLP_hidden_size=256
        freMLP_embed_size=8
    fi
    
    # 构建模型ID，与训练时一致
    local model_id="${data_name}_${pred_len}_${ma_type}_${model_name}_${data_type}_ftM_sl${seq_len}_ll48_pl${pred_len}_Exp_0"
    
    # 确保使用test_model.py而不是run_test.py
    echo "执行: python $PROJECT_ROOT/test_model.py ..."
    
    # 运行我们的独立测试脚本
    CUDA_VISIBLE_DEVICES=$gpu_id python "$PROJECT_ROOT/test_model.py" \
      --model_id $model_id \
      --root_path "$PROJECT_ROOT/dataset/" \
      --data_path $data_path \
      --model $model_name \
      --data $data_type \
      --features M \
      --seq_len $seq_len \
      --pred_len $pred_len \
      --enc_in $enc_in \
      --ma_type $ma_type \
      --alpha $alpha \
      --beta $beta \
      --use_revin 1 \
      --batch_size 32 \
      --des 'Exp' \
      --itr 1 \
      --checkpoints "$PROJECT_ROOT/checkpoints_best/" \
      --result_file "$PROJECT_ROOT/$result_file" \
      $cycle_param \
      --FreMLP_hidden_size $FreMLP_hidden_size \
      --FreMLP_embed_size $freMLP_embed_size \
      --model_type $model_type \
      --plot_result \
      --save_prediction \
      --num_workers 0 \
      --gpu 0 > $log_file 2>&1
    
    local exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        echo "警告: GPU $gpu_id 上的测试失败，退出代码 $exit_code"
        echo "查看详细日志: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "错误日志最后20行:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "成功: GPU $gpu_id 上的测试完成"
    fi
    
    # 任务完成，标记GPU为空闲
    gpu_status[$gpu_id]="free"
    echo "完成在GPU $gpu_id 上测试: $model_name 数据集: $data_name 预测长度: $pred_len"
}

# 显示当前进度信息
show_status() {
    echo "--------------------"
    echo "当前进度: $task_index / $total_tasks 已分配任务"
    for ((i=0; i<$NUM_GPUS; i++)); do
        echo "GPU $i: ${gpu_status[$i]}"
    done
    echo "--------------------"
}

# 初始任务分配
task_index=0
for gpu_id in $(seq 0 $((NUM_GPUS-1))); do
    if [ $task_index -lt $total_tasks ]; then
        # 解析任务
        IFS=' ' read -r model_name pred_len data_name <<< "${tasks[$task_index]}"
        
        # 在后台运行任务
        run_test $gpu_id "$model_name" "$pred_len" "$data_name" &
        
        # 增加任务索引
        task_index=$((task_index + 1))
    fi
done

# 持续检查并分配新任务
while [ $task_index -lt $total_tasks ]; do
    for gpu_id in $(seq 0 $((NUM_GPUS-1))); do
        # 检查GPU是否空闲
        if [ "${gpu_status[$gpu_id]}" = "free" ]; then
            if [ $task_index -lt $total_tasks ]; then
                # 解析任务
                IFS=' ' read -r model_name pred_len data_name <<< "${tasks[$task_index]}"
                
                # 在后台运行任务
                run_test $gpu_id "$model_name" "$pred_len" "$data_name" &
                
                # 增加任务索引
                task_index=$((task_index + 1))
                
                # 显示状态更新
                show_status
            else
                # 没有更多任务
                break
            fi
        fi
    done
    
    # 短暂延迟，减少CPU占用
    sleep 5
done

# 等待所有任务完成
wait
echo "所有测试任务已成功完成！" 