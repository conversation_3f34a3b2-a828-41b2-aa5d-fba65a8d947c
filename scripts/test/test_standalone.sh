#!/bin/bash

# 确保在项目根目录下执行
cd "$(dirname "$0")/../.." || { echo "无法切换到项目根目录"; exit 1; }
PROJECT_ROOT=$(pwd)
echo "当前工作目录：$PROJECT_ROOT"

ma_type=ema
alpha=0.3
beta=0.3

result_file="standalone_test_results.txt"

if [ ! -d "./test_plots" ]; then
    mkdir ./test_plots
fi

model_name=xPatch
seq_len=96

# 检测可用的GPU
NUM_GPUS=$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)
echo "检测到 $NUM_GPUS 个可用GPU"

# 如果没有检测到GPU，默认设置为1以避免错误
if [ "$NUM_GPUS" -eq 0 ]; then
    echo "警告: 未检测到GPU，默认设置为1"
    NUM_GPUS=1
fi

# 确保test_model.py存在
if [ ! -f "$PROJECT_ROOT/test_model.py" ]; then
    echo "错误: 在 $PROJECT_ROOT 中找不到 test_model.py"
    exit 1
fi

# 创建任务队列
tasks=()

# 添加所有任务到队列
for pred_len in 96 192 336 720
do
    # ETTh1
    tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7")
    
    # # 取消注释来测试其他数据集
    # # ETTh2
    # tasks+=("$model_name $pred_len ETTh2 ETTh2.csv ETTh2 7")
    
    # # ETTm1
    # tasks+=("$model_name $pred_len ETTm1 ETTm1.csv ETTm1 7")
    
    # # ETTm2
    # tasks+=("$model_name $pred_len ETTm2 ETTm2.csv ETTm2 7")
    
    # # Weather
    # tasks+=("$model_name $pred_len weather weather.csv custom 21")
    
    # # Traffic
    # tasks+=("$model_name $pred_len traffic traffic.csv custom 862")
    
    # # Electricity
    # tasks+=("$model_name $pred_len electricity electricity.csv custom 321")
    
    # # Exchange
    # tasks+=("$model_name $pred_len exchange exchange_rate.csv custom 8")
    
    # # Solar
    # tasks+=("$model_name $pred_len solar solar.txt Solar 137")
done

# 查看总任务数
total_tasks=${#tasks[@]}
echo "总共需要运行的任务数: $total_tasks"

# 初始化GPU状态数组
declare -a gpu_status=()
for ((i=0; i<$NUM_GPUS; i++)); do
    gpu_status[$i]="free"
done

# 运行测试任务
run_test() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    local data_path=$5
    local data_type=$6
    local enc_in=$7
    
    # 设置输出文件
    local log_file="$PROJECT_ROOT/test_plots/standalone_${model_name}_${data_name}_${seq_len}_${pred_len}_test.log"
    
    echo "在GPU $gpu_id 上测试模型: $model_name 数据集: $data_name 预测长度: $pred_len"
    
    # 设置GPU状态为忙
    gpu_status[$gpu_id]="busy"
    
    # 构建模型ID，与训练时一致
    local model_id="${data_name}_${pred_len}_${ma_type}"
    
    # 设置其他参数
    local cycle_param=""
    if [[ $data_name != "solar" ]]; then
        cycle_param="--cycle 24"
    fi
    
    # 确保我们使用的是test_model.py而不是run_test.py
    echo "执行: python $PROJECT_ROOT/test_model.py ..."
    
    # 运行新的独立测试脚本
    CUDA_VISIBLE_DEVICES=$gpu_id python "$PROJECT_ROOT/test_model.py" \
      --model_id $model_id \
      --root_path "$PROJECT_ROOT/dataset/" \
      --data_path $data_path \
      --model $model_name \
      --data $data_type \
      --features M \
      --seq_len $seq_len \
      --pred_len $pred_len \
      --enc_in $enc_in \
      --ma_type $ma_type \
      --alpha $alpha \
      --beta $beta \
      --use_revin 1 \
      --batch_size 32 \
      --checkpoints "$PROJECT_ROOT/checkpoints_best/" \
      --result_file "$PROJECT_ROOT/$result_file" \
      $cycle_param \
      --plot_result \
      --save_prediction \
      --num_workers 0 \
      --gpu 0 > $log_file 2>&1
    
    local exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        echo "警告: GPU $gpu_id 上的测试失败，退出代码 $exit_code"
        echo "查看详细日志: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "错误日志最后20行:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "成功: GPU $gpu_id 上的测试成功完成"
    fi
    
    # 任务完成，标记GPU为空闲
    gpu_status[$gpu_id]="free"
    echo "完成在GPU $gpu_id 上测试: $model_name 数据集: $data_name 预测长度: $pred_len"
}

# 显示当前进度信息
show_status() {
    echo "--------------------"
    echo "当前进度: $task_index / $total_tasks 已分配任务"
    for ((i=0; i<$NUM_GPUS; i++)); do
        echo "GPU $i: ${gpu_status[$i]}"
    done
    echo "--------------------"
}

# 初始任务分配
task_index=0
for gpu_id in $(seq 0 $((NUM_GPUS-1))); do
    if [ $task_index -lt $total_tasks ]; then
        # 解析任务
        IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in <<< "${tasks[$task_index]}"
        
        # 在后台运行任务
        run_test $gpu_id "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" &
        
        # 增加任务索引
        task_index=$((task_index + 1))
    fi
done

# 持续检查并分配新任务
while [ $task_index -lt $total_tasks ]; do
    for gpu_id in $(seq 0 $((NUM_GPUS-1))); do
        # 检查GPU是否空闲
        if [ "${gpu_status[$gpu_id]}" = "free" ]; then
            if [ $task_index -lt $total_tasks ]; then
                # 解析任务
                IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in <<< "${tasks[$task_index]}"
                
                # 在后台运行任务
                run_test $gpu_id "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" &
                
                # 增加任务索引
                task_index=$((task_index + 1))
                
                # 显示状态更新
                show_status
            else
                # 没有更多任务
                break
            fi
        fi
    done
    
    # 短暂延迟，减少CPU占用
    sleep 5
done

# 等待所有任务完成
wait
echo "所有测试任务已成功完成！" 