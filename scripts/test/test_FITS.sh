#!/bin/bash
echo "==============================================="
echo "运行FITS模型测试脚本"
echo "==============================================="

ma_type=ema
alpha=0.3
beta=0.3

result_file="FITS_test_results.txt"

if [ ! -d "./test_plots" ]; then
    mkdir ./test_plots
fi

# 明确锁定模型名称为FITS
model_name=FITS
seq_len=96  # 所有脚本都使用96作为序列长度，按照要求

# 打印确认信息
echo "模型名称: $model_name"
echo "序列长度: $seq_len"

# 检测可用的GPU数量
NUM_GPUS=$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)
echo "Detected $NUM_GPUS available GPUs"

# 如果没有检测到GPU，默认设置为1以避免错误
if [ "$NUM_GPUS" -eq 0 ]; then
    echo "Warning: No GPUs detected, defaulting to 1"
    NUM_GPUS=1
fi

# 创建任务队列 - 使用各个脚本中的特定参数
declare -a tasks=()

# ETTh1 - 参数取自训练脚本
for pred_len in 96 192 336 720
do
    # 使用train_mode=1
    tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7 24 30 20 7 1 0 24")
done

# ETTh2 - 参数取自训练脚本
for pred_len in 96 192 336 720
do
    # 使用train_mode=1
    tasks+=("$model_name $pred_len ETTh2 ETTh2.csv ETTh2 7 24 30 20 7 1 0 24")
done

# Exchange - 参数取自训练脚本
for pred_len in 96 192 336 720
do
    # 使用train_mode=1，对于exchange数据集使用更小的学习率
    tasks+=("$model_name $pred_len exchange exchange_rate.csv custom 8 96 30 20 10 1 0 48")
done

# ETTm1 - 参数取自训练脚本
for pred_len in 96 192 336 720
do
    # 使用train_mode=1
    tasks+=("$model_name $pred_len ETTm1 ETTm1.csv ETTm1 7 96 30 20 14 1 0 96")
done

# ETTm2 - 参数取自训练脚本
for pred_len in 96 192 336 720
do
    # 使用train_mode=1
    tasks+=("$model_name $pred_len ETTm2 ETTm2.csv ETTm2 7 96 30 20 14 1 0 96")
done

# Weather - 参数取自训练脚本
for pred_len in 96 192 336 720
do
    # 使用train_mode=1
    tasks+=("$model_name $pred_len weather weather.csv custom 21 144 30 10 8 1 1 144")
done

# Electricity - 参数取自训练脚本
for pred_len in 96 192 336 720
do
    # 使用train_mode=1
    tasks+=("$model_name $pred_len electricity electricity.csv custom 321 168 30 20 8 1 0 48")
done

# Solar - 参数取自训练脚本
for pred_len in 96 192 336 720
do
    # 使用train_mode=1
    tasks+=("$model_name $pred_len solar solar.txt Solar 137 144 30 20 8 1 1 48")
done

# Traffic - 参数取自训练脚本
for pred_len in 96 192 336 720
do
    # 使用train_mode=1
    tasks+=("$model_name $pred_len traffic traffic.csv custom 862 168 30 10 8 1 0 48")
done

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 为每个GPU准备任务列表 - 均衡分配，避免相似任务集中在同一GPU
TASKS_PER_GPU=$((total_tasks / NUM_GPUS + (total_tasks % NUM_GPUS > 0)))
echo "Each GPU will handle approximately $TASKS_PER_GPU tasks"

# 打乱任务列表，以更均匀地分配不同类型的任务
shuffled_tasks=()
# 将任务以交错方式添加到新列表
for i in $(seq 0 7); do  # 8个数据集
    for j in $(seq 0 3); do  # 4个预测长度
        index=$((i*4 + j))
        if [ $index -lt ${#tasks[@]} ]; then
            shuffled_tasks+=("${tasks[$index]}")
        fi
    done
done

# 重新赋值任务列表
tasks=("${shuffled_tasks[@]}")

# 初始化GPU状态数组
declare -a gpu_status=()
for ((i=0; i<$NUM_GPUS; i++)); do
    gpu_status[$i]="free"
done

# 运行测试任务
run_test() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    local data_path=$5
    local data_type=$6
    local enc_in=$7
    local cycle=${8}
    local train_epochs=${9}
    local patience=${10}
    local H_order=${11}
    local train_mode=${12}
    local use_individual=${13}
    local base_T=${14}
    
    # 设置输出文件
    local log_file="test_plots/${model_name}_${data_name}_${seq_len}_${pred_len}_TM${train_mode}_H${H_order}_test.log"
    
    echo "Testing model on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len, H_order $H_order, train_mode $train_mode"
    
    # 设置GPU状态为忙
    gpu_status[$gpu_id]="busy"
    
    # 构建模型ID，与训练时一致
    local model_id="${data_name}_${pred_len}_${ma_type}_${model_name}_${data_type}_ftM_sl${seq_len}_ll48_pl${pred_len}_Exp_0"
    
    # 构建命令参数
    local cmd="CUDA_VISIBLE_DEVICES=$gpu_id python run_test.py \
      --model_id $model_id \
      --root_path ./dataset/ \
      --data_path $data_path \
      --model $model_name \
      --data $data_type \
      --features M \
      --seq_len $seq_len \
      --pred_len $pred_len \
      --enc_in $enc_in \
      --train_mode $train_mode \
      --H_order $H_order \
      --ma_type $ma_type \
      --alpha $alpha \
      --beta $beta \
      --revin 1 \
      --result_file $result_file"
    
    # 根据数据集添加特殊参数
    if [ ! -z "$base_T" ] && [ "$base_T" != "0" ]; then
        cmd="$cmd --base_T $base_T"
    fi
    
    if [ "$use_individual" -eq 1 ]; then
        cmd="$cmd --individual"
    fi
    
    cmd="$cmd --plot_result --save_prediction --gpu $gpu_id > $log_file 2>&1"
    
    # 执行命令
    eval $cmd
    
    local exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Test on GPU $gpu_id failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Test completed successfully on GPU $gpu_id"
    fi
    
    # 任务完成，标记GPU为空闲
    gpu_status[$gpu_id]="free"
    echo "Test completed on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len"
}

# 显示当前进度信息
show_status() {
    echo "--------------------"
    echo "Current progress: $task_index / $total_tasks tasks assigned"
    for ((i=0; i<$NUM_GPUS; i++)); do
        echo "GPU $i: ${gpu_status[$i]}"
    done
    echo "--------------------"
}

# 初始任务分配
task_index=0
for gpu_id in $(seq 0 $((NUM_GPUS-1))); do
    if [ $task_index -lt $total_tasks ]; then
        # 解析任务
        IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in cycle train_epochs patience H_order train_mode use_individual base_T <<< "${tasks[$task_index]}"
        
        # 在后台运行任务
        run_test $gpu_id "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" "$cycle" "$train_epochs" "$patience" "$H_order" "$train_mode" "$use_individual" "$base_T" &
        
        # 增加任务索引
        task_index=$((task_index + 1))
    fi
done

# 持续检查并分配新任务
while [ $task_index -lt $total_tasks ]; do
    for gpu_id in $(seq 0 $((NUM_GPUS-1))); do
        # 检查GPU是否空闲
        if [ "${gpu_status[$gpu_id]}" = "free" ]; then
            if [ $task_index -lt $total_tasks ]; then
                # 解析任务
                IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in cycle train_epochs patience H_order train_mode use_individual base_T <<< "${tasks[$task_index]}"
                
                # 在后台运行任务
                run_test $gpu_id "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" "$cycle" "$train_epochs" "$patience" "$H_order" "$train_mode" "$use_individual" "$base_T" &
                
                # 增加任务索引
                task_index=$((task_index + 1))
                
                # 显示状态更新
                show_status
            else
                # 没有更多任务
                break
            fi
        fi
    done
    
    # 短暂延迟，减少CPU占用
    sleep 5
done

# 等待所有任务完成
wait
echo "All tests completed successfully!" 