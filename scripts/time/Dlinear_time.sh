#!/bin/bash
ma_type=ema
alpha=0.3
beta=0.3

result_file="dlinear_result.txt"

if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/time" ]; then
    mkdir ./logs/time
fi

model_name=DLinear
seq_len=720

# 信号处理 - 添加清理函数
cleanup() {
    echo "Caught interrupt signal. Terminating all running tasks..."
    # 杀死所有子进程
    pkill -P $$
    # 给进程一点时间来优雅终止
    sleep 2
    # 如果还有子进程存活，强制终止
    pkill -9 -P $$ 2>/dev/null
    echo "All tasks terminated."
    exit 1
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 使用固定的GPU ID为0
GPU_ID=0
echo "Using GPU $GPU_ID for all tasks"

# 创建任务队列
tasks=()

# 添加所有任务到队列 - 预测长度96
# pred_len=96

# # ETTh1
# tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7 8 0.005")

# # ETTm1
# tasks+=("$model_name $pred_len ETTm1 ETTm1.csv ETTm1 7 8 0.0001")

# # Weather
# tasks+=("$model_name $pred_len weather weather.csv custom 21 8 0.001")

# 添加预测长度720的任务
pred_len=720

# ETTh1 - 参考unified脚本但保持batch_size=8
tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7 8 0.005")

# ETTm1 - 参考unified脚本但保持batch_size=8
tasks+=("$model_name $pred_len ETTm1 ETTm1.csv ETTm1 7 8 0.01")

# Weather - 参考unified脚本但保持batch_size=8
tasks+=("$model_name $pred_len weather weather.csv custom 21 8 0.001")

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 运行任务的函数
run_task() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    local data_path=$5
    local data_type=$6
    local enc_in=$7
    local batch_size=$8
    local learning_rate=$9
    local log_file="logs/time/${model_name}_${data_name}_${seq_len}_${pred_len}.log"
    
    echo "Starting task: $model_name on $data_name with pred_len $pred_len"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 设置其他参数
    local cycle_param=""
    if [[ $data_name != "solar" ]]; then
        cycle_param="--cycle 24"
    fi
    
    # 运行任务
    CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
      --is_training 1 \
      --root_path ./dataset/ \
      --data_path $data_path \
      --model_id ${data_name}_${pred_len}_${ma_type} \
      --model $model_name \
      --data $data_type \
      --features M \
      --seq_len $seq_len \
      --pred_len $pred_len \
      --enc_in $enc_in \
      --des 'Exp' \
      --itr 1 \
      --batch_size $batch_size \
      --learning_rate $learning_rate \
      --lradj 'sigmoid' \
      --train_epochs 3 \
      --ma_type $ma_type \
      --alpha $alpha \
      --beta $beta \
      --result_file $result_file \
      $cycle_param \
      --gpu 0 > $log_file 2>&1
    
    local exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Task failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Task completed successfully"
    fi
    
    echo "Task completed: $model_name on $data_name with pred_len $pred_len (took ${duration}s)"
}

# 顺序执行任务
task_index=0
echo "Starting sequential task execution..."

for ((task_index=0; task_index<total_tasks; task_index++)); do
    # 解析任务
    IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in batch_size learning_rate <<< "${tasks[$task_index]}"
    
    # 执行任务
    run_task $GPU_ID "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" "$batch_size" "$learning_rate"
    
    # 显示进度
    echo "Progress: $((task_index+1)) / $total_tasks tasks completed"
done

echo "All tasks completed successfully!"
