#!/bin/bash
ma_type=ema
alpha=0.3
beta=0.3
result_file="itransformer_result.txt"

if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/time" ]; then
    mkdir ./logs/time
fi

# 配置参数
seq_len=720
model_type='linear'

FreMLP_hidden_size=256
freMLP_embed_size=8

# 信号处理 - 添加清理函数
cleanup() {
    echo "Caught interrupt signal. Terminating all running tasks..."
    # 杀死所有子进程
    pkill -P $$
    # 给进程一点时间来优雅终止
    sleep 2
    # 如果还有子进程存活，强制终止
    pkill -9 -P $$ 2>/dev/null
    echo "All tasks terminated."
    exit 1
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 使用固定的GPU ID为0
GPU_ID=1
echo "Using GPU $GPU_ID for all tasks"

# 创建任务队列
declare -a model_array=("iTransformer")
declare -a pred_len_array=(720)
declare -a data_array=("ETTh1" "ETTm1" "weather")

# 创建所有任务组合
tasks=()
for pred_len in "${pred_len_array[@]}"; do
    for model_name in "${model_array[@]}"; do
        for data_name in "${data_array[@]}"; do
            tasks+=("$model_name $pred_len $data_name")
        done
    done
done

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 运行任务的函数
run_task() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    local log_file="logs/time/${model_name}_${data_name}_${seq_len}_${pred_len}.log"
    
    echo "Starting task: $model_name on $data_name with pred_len $pred_len"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 根据数据集设置适当的参数
    local data_path=""
    local enc_in=""
    local batch_size=8  # 固定batch_size为8
    local base_learning_rate=""  # 数据集的基准学习率
    local learning_rate=""       # 根据预测长度调整后的学习率
    local data_type="custom"
    local cycle_param="--cycle 24"
    
    # 基准学习率设置
    if [[ $data_name == "weather" ]]; then
        data_path="weather.csv"
        enc_in=21
        base_learning_rate=0.0002
        e_layers=3
        d_model=512
        d_ff=512
        
    elif [[ $data_name == "ETTh1" ]]; then
        data_path="ETTh1.csv"
        data_type="ETTh1"
        enc_in=7
        base_learning_rate=0.0001
        e_layers=2
        
        # Parameters vary based on prediction length for ETTh1
        if [[ $pred_len -le 192 ]]; then
            d_model=256
            d_ff=256
        else
            d_model=512
            d_ff=512
        fi
        
    elif [[ $data_name == "ETTm1" ]]; then
        data_path="ETTm1.csv"
        data_type="ETTm1"
        enc_in=7
        base_learning_rate=0.0001
        e_layers=2
        d_model=128
        d_ff=128
    fi
    
    # itransformer 学习率固定为0.001
    learning_rate=$base_learning_rate
    
    # 检查学习率是否为0，如果是则使用最小有效值
    if [[ "$learning_rate" == "0" || "$learning_rate" == "0.0" || "$learning_rate" == "0.00000000" ]]; then
        echo "Warning: Learning rate calculated as zero. Using minimum value instead."
        learning_rate="0.0001"   # 对其他数据使用较小值
    fi
    
    # 打印调整后的学习率
    echo "Base learning rate: $base_learning_rate, Adjusted learning rate: $learning_rate"
    
    
    # 运行任务
    CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
      --is_training 1 \
      --root_path ./dataset/ \
      --data_path $data_path \
      --model_id ${data_name}_${pred_len}_${ma_type} \
      --model $model_name \
      --data $data_type \
      --features M \
      --seq_len $seq_len \
      --pred_len $pred_len \
      --enc_in $enc_in \
      --dec_in $enc_in \
      --c_out $enc_in \
      --des 'Exp' \
      --itr 1 \
      --batch_size $batch_size \
      --FreMLP_embed_size $freMLP_embed_size \
      --FreMLP_hidden_size $FreMLP_hidden_size \
      --learning_rate $learning_rate \
      --e_layers $e_layers \
      --d_model $d_model \
      --d_ff $d_ff \
      --result_file $result_file \
      $cycle_param \
      --model_type $model_type \
      --lradj 'sigmoid' \
      --hidden_size 512 \
      --SCI 0 \
      --train_epochs 3 \
      --ma_type $ma_type \
      --alpha $alpha \
      --beta $beta \
      --gpu 0 > $log_file 2>&1
    
    local exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Task failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Task completed successfully"
    fi
    
    echo "Task completed: $model_name on $data_name with pred_len $pred_len (took ${duration}s)"
}

# 顺序执行任务
task_index=0
echo "Starting sequential task execution..."

for ((task_index=0; task_index<total_tasks; task_index++)); do
    # 解析任务
    IFS=' ' read -r model_name pred_len data_name <<< "${tasks[$task_index]}"
    
    # 执行任务
    run_task $GPU_ID "$model_name" "$pred_len" "$data_name"
    
    # 显示进度
    echo "Progress: $((task_index+1)) / $total_tasks tasks completed"
done

echo "All tasks completed successfully!"

