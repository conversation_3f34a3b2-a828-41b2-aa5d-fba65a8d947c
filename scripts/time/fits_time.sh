#!/bin/bash
echo "==============================================="
echo "运行FITS模型统一训练脚本 - 版本1.0"
echo "==============================================="

ma_type=ema
alpha=0.3
beta=0.3

result_file="FITS_result.txt"

if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/time" ]; then
    mkdir ./logs/time
fi

# 明确锁定模型名称为FITS
model_name=FITS
seq_len=720  # 所有脚本都使用96作为序列长度，按照要求

# 打印确认信息
echo "模型名称: $model_name"
echo "序列长度: $seq_len"

# 信号处理 - 添加清理函数
cleanup() {
    echo "Caught interrupt signal. Terminating all running tasks..."
    # 杀死所有子进程
    pkill -P $$
    # 给进程一点时间来优雅终止
    sleep 2
    # 如果还有子进程存活，强制终止
    pkill -9 -P $$ 2>/dev/null
    echo "All tasks terminated."
    exit 1
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 使用固定的GPU ID为0
GPU_ID=0
echo "Using GPU $GPU_ID for all tasks"

# 创建任务队列 - 使用各个脚本中的特定参数
declare -a tasks=()

# 预测长度96的任务
# pred_len=96

# # ETTh1 - 原H_order=6，调整为H_order=8，并明确base_T=24
# tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7 8 0.0005 24 3 20 7 1 0 24")

# # ETTm1 - 原H_order=14，保持相同，已有base_T=96
# tasks+=("$model_name $pred_len ETTm1 ETTm1.csv ETTm1 7 8 0.0005 96 3 20 14 1 0 96")

# # Weather - 原H_order=12，调整为H_order=8，保持base_T=144
# tasks+=("$model_name $pred_len weather weather.csv custom 21 8 0.0005 144 3 10 8 1 1 144")

# 添加预测长度720的任务
pred_len=720

# ETTh1 - 使用原文提供的720特定参数但保持batch_size=8
tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7 8 0.0005 24 3 20 7 1 0 24")

# ETTm1 - 使用原文提供的720特定参数但保持batch_size=8
tasks+=("$model_name $pred_len ETTm1 ETTm1.csv ETTm1 7 8 0.0005 96 3 20 14 1 0 96")

# Weather - 使用原文提供的720特定参数但保持batch_size=8
tasks+=("$model_name $pred_len weather weather.csv custom 21 8 0.0005 144 3 10 8 1 1 144")

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 添加调试输出，检查任务内容
echo "Task format check:"
echo "${tasks[0]}"

# 运行任务的函数
run_task() {
    local gpu_id=$1
    local model_name=$2  # 无需覆盖全局model_name
    local pred_len=$3
    local data_name=$4
    local data_path=$5
    local data_type=$6
    local enc_in=$7
    local batch_size=$8
    local learning_rate=$9
    local cycle=${10}
    local train_epochs=${11}
    local patience=${12}
    local H_order=${13}
    local train_mode=${14}
    local use_individual=${15}
    local base_T=${16}
    
    # 确认参数均已正确传递
    echo "Parameters debug:"
    echo "  model_name: $model_name (确认是FITS)"
    echo "  pred_len: $pred_len"
    echo "  data_name: $data_name"
    echo "  H_order: $H_order"
    echo "  train_mode: $train_mode"
    echo "  use_individual: $use_individual"
    echo "  base_T: $base_T"
    
    local log_file="logs/time/${model_name}_${data_name}_${seq_len}_${pred_len}_TM${train_mode}_H${H_order}.log"
    echo "  log_file: $log_file"
    
    echo "Starting task: $model_name on $data_name with pred_len $pred_len, H_order $H_order, train_mode $train_mode"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 构建命令参数
    local cmd="CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
      --is_training 1 \
      --root_path ./dataset/ \
      --data_path $data_path \
      --model_id ${data_name}_${pred_len}_${ma_type}_${model_name}_${data_type}_ftM_sl${seq_len}_ll48_pl${pred_len}_Exp_0 \
      --model $model_name \
      --data $data_type \
      --features M \
      --seq_len $seq_len \
      --pred_len $pred_len \
      --enc_in $enc_in \
      --des 'Exp' \
      --train_mode $train_mode \
      --H_order $H_order \
      --train_epochs $train_epochs \
      --patience $patience \
      --batch_size $batch_size \
      --learning_rate $learning_rate \
      --itr 1 \
      --ma_type $ma_type \
      --alpha $alpha \
      --beta $beta \
      --result_file $result_file"
    
    # 根据数据集添加特殊参数
    if [ ! -z "$base_T" ] && [ "$base_T" != "0" ]; then
        cmd="$cmd --base_T $base_T"
    fi
    
    if [ "$use_individual" -eq 1 ]; then
        cmd="$cmd --individual"
    fi
    
    cmd="$cmd --gpu 0 > $log_file 2>&1"
    
    # 执行命令
    eval $cmd
    
    local exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Task failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Task completed successfully"
    fi
    
    echo "Task completed: $model_name on $data_name with pred_len $pred_len (took ${duration}s)"
}

# 顺序执行任务
task_index=0
echo "Starting sequential task execution..."

for ((task_index=0; task_index<total_tasks; task_index++)); do
    # 解析任务
    IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in batch_size learning_rate cycle train_epochs patience H_order train_mode use_individual base_T <<< "${tasks[$task_index]}"
    
    # 执行任务
    run_task $GPU_ID "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" "$batch_size" "$learning_rate" "$cycle" "$train_epochs" "$patience" "$H_order" "$train_mode" "$use_individual" "$base_T"
    
    # 显示进度
    echo "Progress: $((task_index+1)) / $total_tasks tasks completed"
done

echo "All tasks completed successfully!"
