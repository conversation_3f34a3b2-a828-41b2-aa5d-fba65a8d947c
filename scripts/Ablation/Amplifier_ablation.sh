#!/bin/bash
ma_type=ema
alpha=0.3
beta=0.3

result_file="result_ablation_attn_no2.txt"

if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/ablation/attn" ]; then
    mkdir ./logs/ablation/attn
fi

model_name=Amplifier
seq_len=96

# 信号处理 - 添加清理函数
cleanup() {
    echo "Caught interrupt signal. Terminating all running tasks..."
    # 杀死所有子进程
    pkill -P $$
    # 给进程一点时间来优雅终止
    sleep 2
    # 如果还有子进程存活，强制终止
    pkill -9 -P $$ 2>/dev/null
    echo "All tasks terminated."
    exit 1
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 检测可用的GPU数量
NUM_GPUS=$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)
echo "Detected $NUM_GPUS available GPUs"

# 如果没有检测到GPU，默认设置为1以避免错误
if [ "$NUM_GPUS" -eq 0 ]; then
    echo "Warning: No GPUs detected, defaulting to 1"
    NUM_GPUS=1
fi

# 定义GPU ID数组，与MyModel_multiLen.sh保持一致
declare -a gpu_id=("0" "1" "2" "3")
NUM_GPUS=${#gpu_id[@]}

# 创建任务队列
tasks=()

# 添加所有任务到队列
for pred_len in 96 192 336 720
do
    # ETTh1 - 使用原文提供的特定参数
    if [ "$pred_len" -eq 96 ]; then
        tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7 256 0.02 64 0")
    elif [ "$pred_len" -eq 192 ]; then
        tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7 256 0.02 512 0")
    elif [ "$pred_len" -eq 336 ] || [ "$pred_len" -eq 720 ]; then
        tasks+=("$model_name $pred_len ETTh1 ETTh1.csv ETTh1 7 256 0.03 512 0")
    fi
    
    # # # ETTh2 - 使用与ETTh1相同的参数
    if [ "$pred_len" -eq 96 ]; then
        tasks+=("$model_name $pred_len ETTh2 ETTh2.csv ETTh2 7 256 0.02 64 0")
    elif [ "$pred_len" -eq 192 ]; then
        tasks+=("$model_name $pred_len ETTh2 ETTh2.csv ETTh2 7 256 0.02 512 0")
    elif [ "$pred_len" -eq 336 ] || [ "$pred_len" -eq 720 ]; then
        tasks+=("$model_name $pred_len ETTh2 ETTh2.csv ETTh2 7 256 0.03 512 0")
    fi
    
    # # # ETTm1 - 使用原文提供的特定参数
    if [ "$pred_len" -eq 96 ] || [ "$pred_len" -eq 192 ] || [ "$pred_len" -eq 336 ]; then
        tasks+=("$model_name $pred_len ETTm1 ETTm1.csv ETTm1 7 32 0.02 128 0")
    elif [ "$pred_len" -eq 720 ]; then
        tasks+=("$model_name $pred_len ETTm1 ETTm1.csv ETTm1 7 256 0.005 128 0")
    fi
    
    # # # ETTm2 - 使用与ETTm1相同的参数
    if [ "$pred_len" -eq 96 ] || [ "$pred_len" -eq 192 ] || [ "$pred_len" -eq 336 ]; then
        tasks+=("$model_name $pred_len ETTm2 ETTm2.csv ETTm2 7 32 0.02 128 0")
    elif [ "$pred_len" -eq 720 ]; then
        tasks+=("$model_name $pred_len ETTm2 ETTm2.csv ETTm2 7 256 0.005 128 0")
    fi
    
    # # Exchange
    # tasks+=("$model_name $pred_len exchange exchange_rate.csv custom 8 32 0.00001")
    
    # # Electricity - 使用原文提供的特定参数
    # if [ "$pred_len" -eq 96 ]; then
    #     tasks+=("$model_name $pred_len electricity electricity.csv custom 321 16 0.005 512 1")
    # elif [ "$pred_len" -eq 192 ]; then
    #     tasks+=("$model_name $pred_len electricity electricity.csv custom 321 16 0.002 512 1")
    # elif [ "$pred_len" -eq 336 ] || [ "$pred_len" -eq 720 ]; then
    #     tasks+=("$model_name $pred_len electricity electricity.csv custom 321 16 0.0005 1024 1")
    # fi
    
    # Solar
    tasks+=("$model_name $pred_len solar solar.txt Solar 137 512 0.005")

    #  # # # Weather
    tasks+=("$model_name $pred_len weather weather.csv custom 21 2048 0.0005")
    
    # # Traffic
    # tasks+=("$model_name $pred_len traffic traffic.csv custom 862 96 0.005")
done

# 查看总任务数
total_tasks=${#tasks[@]}
echo "Total tasks to run: $total_tasks"

# 运行任务的函数
run_task() {
    local gpu_id=$1
    local model_name=$2
    local pred_len=$3
    local data_name=$4
    local data_path=$5
    local data_type=$6
    local enc_in=$7
    local batch_size=$8
    local learning_rate=$9
    local hidden_size=${10:-512}  # 默认hidden_size为512
    local sci_flag=${11:-1}       # 默认SCI为1
    local log_file="logs/ablation/2.3_attn_seasonal_trend/${model_name}_${data_name}_${seq_len}_${pred_len}_attn.log"
    
    echo "Starting task on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 设置其他参数
    local cycle_param=""
    if [[ $data_name != "solar" ]]; then
        cycle_param="--cycle 24"
    fi
    
    # 为特定数据集使用特定设置
    if [[ $data_name == "electricity" ]]; then
        # 运行任务 - 使用原文的电力数据集命令参数
        CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
          --is_training 1 \
          --root_path ./dataset/electricity/ \
          --data_path $data_path \
          --model_id ${model_name}_${data_name}_sl${seq_len}_pl${pred_len}_attn\
          --model $model_name \
          --data $data_type \
          --features M \
          --seq_len $seq_len \
          --label_len 48 \
          --pred_len $pred_len \
          --enc_in $enc_in \
          --hidden_size $hidden_size \
          --SCI $sci_flag \
          --batch_size $batch_size \
          --learning_rate $learning_rate \
          --des 'Exp' \
          --itr 1 \
          --ma_type $ma_type \
          --alpha $alpha \
          --beta $beta \
          --result_file $result_file \
          --gpu 0 > $log_file 2>&1
    elif [[ $data_name == "ETTh1" ]]; then
        # 运行任务 - 使用原文的ETTh1数据集命令参数
        CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
          --is_training 1 \
          --root_path ./dataset/ETT-small/ \
          --data_path $data_path \
          --model_id ${model_name}_${data_name}_sl${seq_len}_pl${pred_len}_attn\
          --model $model_name \
          --data $data_type \
          --features M \
          --seq_len $seq_len \
          --label_len 48 \
          --pred_len $pred_len \
          --enc_in $enc_in \
          --hidden_size $hidden_size \
          --SCI $sci_flag \
          --batch_size $batch_size \
          --learning_rate $learning_rate \
          --des 'Exp' \
          --itr 1 \
          --ma_type $ma_type \
          --alpha $alpha \
          --beta $beta \
          --result_file $result_file \
          --gpu 0 > $log_file 2>&1
    elif [[ $data_name == "ETTh2" ]]; then
        # 运行任务 - 使用与ETTh1相同的参数
        CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
          --is_training 1 \
          --root_path ./dataset/ETT-small/ \
          --data_path $data_path \
          --model_id ${model_name}_${data_name}_sl${seq_len}_pl${pred_len}_attn\
          --model $model_name \
          --data $data_type \
          --features M \
          --seq_len $seq_len \
          --label_len 48 \
          --pred_len $pred_len \
          --enc_in $enc_in \
          --hidden_size $hidden_size \
          --SCI $sci_flag \
          --batch_size $batch_size \
          --learning_rate $learning_rate \
          --des 'Exp' \
          --itr 1 \
          --ma_type $ma_type \
          --alpha $alpha \
          --beta $beta \
          --result_file $result_file \
          --gpu 0 > $log_file 2>&1
    elif [[ $data_name == "ETTm1" ]]; then
        # 运行任务 - 使用原文的ETTm1数据集命令参数
        CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
          --is_training 1 \
          --root_path ./dataset/ETT-small/ \
          --data_path $data_path \
          --model_id ${model_name}_${data_name}_sl${seq_len}_pl${pred_len}_attn\
          --model $model_name \
          --data $data_type \
          --features M \
          --seq_len $seq_len \
          --label_len 48 \
          --pred_len $pred_len \
          --enc_in $enc_in \
          --hidden_size $hidden_size \
          --SCI $sci_flag \
          --batch_size $batch_size \
          --learning_rate $learning_rate \
          --des 'Exp' \
          --itr 1 \
          --ma_type $ma_type \
          --alpha $alpha \
          --beta $beta \
          --result_file $result_file \
          --gpu 0 > $log_file 2>&1
    elif [[ $data_name == "ETTm2" ]]; then
        # 运行任务 - 使用与ETTm1相同的参数
        CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
          --is_training 1 \
          --root_path ./dataset/ETT-small/ \
          --data_path $data_path \
          --model_id ${model_name}_${data_name}_sl${seq_len}_pl${pred_len}_attn\
          --model $model_name \
          --data $data_type \
          --features M \
          --seq_len $seq_len \
          --label_len 48 \
          --pred_len $pred_len \
          --enc_in $enc_in \
          --hidden_size $hidden_size \
          --SCI $sci_flag \
          --batch_size $batch_size \
          --learning_rate $learning_rate \
          --des 'Exp' \
          --itr 1 \
          --ma_type $ma_type \
          --alpha $alpha \
          --beta $beta \
          --result_file $result_file \
          --gpu 0 > $log_file 2>&1
    else
        # 其他数据集使用原有的命令参数
        CUDA_VISIBLE_DEVICES=$gpu_id python -u run.py \
          --is_training 1 \
          --root_path ./dataset/ \
          --data_path $data_path \
          --model_id ${model_name}_${data_name}_sl${seq_len}_pl${pred_len}_attn\
          --model $model_name \
          --data $data_type \
          --features M \
          --seq_len $seq_len \
          --pred_len $pred_len \
          --enc_in $enc_in \
          --des 'Exp' \
          --itr 1 \
          --batch_size $batch_size \
          --learning_rate $learning_rate \
          --lradj 'sigmoid' \
          --ma_type $ma_type \
          --alpha $alpha \
          --beta $beta \
          --result_file $result_file \
          $cycle_param \
          --gpu 0 > $log_file 2>&1
    fi
    
    local exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $exit_code -ne 0 ]; then
        echo "WARNING: Task on GPU $gpu_id failed with exit code $exit_code"
        echo "Check log file for details: $log_file"
        # 提取日志文件中的最后20行错误信息
        echo "Last 20 lines of error log:"
        tail -n 20 $log_file | grep -E "Error|Exception|Traceback|Failed"
    else
        echo "SUCCESS: Task completed successfully on GPU $gpu_id"
    fi
    
    echo "Task completed on GPU $gpu_id: $model_name on $data_name with pred_len $pred_len (took ${duration}s)"
}

# 显示任务分配方式
echo "Distributing tasks across ${NUM_GPUS} GPUs in a round-robin fashion"

# 启动进程数组
pids=()

# 直接分配任务到GPU上
for ((i=0; i<$total_tasks; i++)); do
    # 计算应该使用哪个GPU
    gpu_idx=$((i % NUM_GPUS))
    current_gpu=${gpu_id[$gpu_idx]}
    
    # 解析任务
    IFS=' ' read -r model_name pred_len data_name data_path data_type enc_in batch_size learning_rate hidden_size sci_flag <<< "${tasks[$i]}"
    
    # 在后台运行任务
    run_task $current_gpu "$model_name" "$pred_len" "$data_name" "$data_path" "$data_type" "$enc_in" "$batch_size" "$learning_rate" "$hidden_size" "$sci_flag" &
    pids+=($!)
    
    # 简单的进度显示
    echo "Started task $((i+1))/$total_tasks on GPU $current_gpu"
    
    # 如果已经分配了所有GPU的任务，稍微等待一会再继续分配
    # 这可以防止一下子启动太多进程
    if (( (i+1) % NUM_GPUS == 0 )); then
        echo "Waiting for current batch of tasks to initialize before continuing..."
        sleep 2
    fi
done

# 等待所有任务完成
echo "All tasks have been started. Waiting for completion..."
wait
echo "All tasks completed successfully!"
