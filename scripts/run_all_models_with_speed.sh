#!/bin/bash

# Create logs directory if it doesn't exist
if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/time" ]; then
    mkdir ./logs/time
fi

# Function to run a model's timing script and then calculate its speed
run_model() {
    local script_name=$1
    local model_name=$2
    
    echo "======================================================"
    echo "Running $model_name timing script: $script_name"
    echo "======================================================"
    
    # Run the model's timing script
    bash $script_name
    
    # Calculate model speeds
    echo "Calculating speeds for $model_name..."
    python scripts/calculate_model_speeds.py --model $model_name
    
    echo "Completed $model_name"
    echo ""
}

# Make sure our models directory exists
cd $(dirname $0)/..

# Define all model timing scripts and their model names
declare -A models=(
    ["scripts/time/xPatch_time.sh"]="xPatch"
    ["scripts/time/Dlinear_time.sh"]="DLinear"
    ["scripts/time/FreTs_time.sh"]="FreTS"
    ["scripts/time/Amplifier_time.sh"]="Amplifier"
    ["scripts/time/FilterNet_time.sh"]="PaiFilter"
    ["scripts/time/fits_time.sh"]="FITS"
    ["scripts/time/itransformer_time.sh"]="iTransformer"
    ["scripts/time/MyModel_time.sh"]="MyModel"
    ["scripts/time/sparseTSF_mlp_time.sh"]="SparseTSF_mlp"
)

# Process command line arguments
if [ $# -eq 0 ]; then
    # Run all models if no arguments provided
    for script in "${!models[@]}"; do
        model="${models[$script]}"
        run_model "$script" "$model"
    done
else
    # Run only the specified models
    for model_name in "$@"; do
        # Find the script for the given model name
        script=""
        for s in "${!models[@]}"; do
            if [ "${models[$s]}" = "$model_name" ]; then
                script=$s
                break
            fi
        done
        
        if [ -n "$script" ]; then
            run_model "$script" "$model_name"
        else
            echo "Error: Unknown model name '$model_name'"
            echo "Available models: ${models[*]}"
        fi
    done
fi

# Print final results
echo "===================================================="
echo "Final Speed Results for All Processed Models"
echo "===================================================="
python scripts/calculate_model_speeds.py

echo "All tasks completed!" 