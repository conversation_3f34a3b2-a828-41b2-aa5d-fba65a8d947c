import os
import re
import pandas as pd
import numpy as np
from openpyxl import Workbook
from openpyxl.styles import <PERSON><PERSON>, PatternFill
from openpyxl.utils import get_column_letter

# Define the input file and output directory
input_file = "result_ablation_attn.txt"
output_dir = "ablation_results"

# Create output directory if it doesn't exist
os.makedirs(output_dir, exist_ok=True)

# Dictionary to store experiment results
results = {}

# Models, datasets, and modules to process
models = ["MyModel", "Amplifier", "xPatch"]
datasets = ["ETTh1", "ETTh2", "ETTm1", "ETTm2", "weather", "solar"]
modules = ["add", "mlp", "attn"]
pred_lengths = [96, 192, 336, 720]

# Function to extract metrics from the result file
def extract_metrics(content, pattern):
    matches = re.findall(pattern, content)
    return matches

# Read the input file
try:
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
except Exception as e:
    print(f"Error reading file {input_file}: {e}")
    exit(1)

# Regular expression pattern to match experiment results
# Format: ModelName_Dataset_sl96_plXXX_module
pattern = r'(MyModel|Amplifier|xPatch)_([A-Za-z0-9]+)_sl96_pl(\d+)_([a-z]+)\s+\nmse:([\d\.]+), mae:([\d\.]+)\nAverage inference time: ([\d\.]+)s per batch'

# Extract all experiment results
experiment_results = extract_metrics(content, pattern)

# Process the extracted data
for result in experiment_results:
    model = result[0]
    dataset = result[1]
    pred_len = int(result[2])
    module = result[3]
    mse = float(result[4])
    mae = float(result[5])
    
    # Initialize nested dictionaries if they don't exist
    if model not in results:
        results[model] = {}
    if dataset not in results[model]:
        results[model][dataset] = {}
    if module not in results[model][dataset]:
        results[model][dataset][module] = {}
    
    # Store the results
    results[model][dataset][module][pred_len] = {
        "MSE": mse,
        "MAE": mae
    }

# Create the DataFrame structure
# Generate column MultiIndex for model+module combinations
column_tuples = []
for model in models:
    for module in modules:
        column_tuples.append((model, module, "MSE"))
        column_tuples.append((model, module, "MAE"))

columns = pd.MultiIndex.from_tuples(column_tuples, names=["Model", "Module", "Metric"])

# Generate row MultiIndex for dataset and prediction length
row_tuples = []
for dataset in datasets:
    for pred_len in pred_lengths:
        row_tuples.append((dataset, str(pred_len)))
    # Add mean row for each dataset
    row_tuples.append((dataset, "Mean"))

# Add overall mean row
row_tuples.append(("Overall", "Mean"))

rows = pd.MultiIndex.from_tuples(row_tuples, names=["Dataset", "Pred_Len"])

# Create the DataFrame
df = pd.DataFrame(index=rows, columns=columns)

# Fill the DataFrame with raw values
for model in models:
    for dataset in datasets:
        if dataset in results[model]:
            for module in modules:
                if module in results[model][dataset]:
                    for pred_len in pred_lengths:
                        if pred_len in results[model][dataset][module]:
                            mse = results[model][dataset][module][pred_len]["MSE"]
                            mae = results[model][dataset][module][pred_len]["MAE"]
                            
                            df.loc[(dataset, str(pred_len)), (model, module, "MSE")] = mse
                            df.loc[(dataset, str(pred_len)), (model, module, "MAE")] = mae

# Calculate dataset means
for model in models:
    for dataset in datasets:
        for module in modules:
            mse_values = []
            mae_values = []
            
            for pred_len in pred_lengths:
                if (dataset, str(pred_len)) in df.index and pd.notna(df.loc[(dataset, str(pred_len)), (model, module, "MSE")]):
                    mse_values.append(df.loc[(dataset, str(pred_len)), (model, module, "MSE")])
                
                if (dataset, str(pred_len)) in df.index and pd.notna(df.loc[(dataset, str(pred_len)), (model, module, "MAE")]):
                    mae_values.append(df.loc[(dataset, str(pred_len)), (model, module, "MAE")])
            
            if mse_values:
                df.loc[(dataset, "Mean"), (model, module, "MSE")] = np.mean(mse_values)
            if mae_values:
                df.loc[(dataset, "Mean"), (model, module, "MAE")] = np.mean(mae_values)

# Calculate overall means
for model in models:
    for module in modules:
        all_mse_values = []
        all_mae_values = []
        
        for dataset in datasets:
            for pred_len in pred_lengths:
                if (dataset, str(pred_len)) in df.index and pd.notna(df.loc[(dataset, str(pred_len)), (model, module, "MSE")]):
                    all_mse_values.append(df.loc[(dataset, str(pred_len)), (model, module, "MSE")])
                
                if (dataset, str(pred_len)) in df.index and pd.notna(df.loc[(dataset, str(pred_len)), (model, module, "MAE")]):
                    all_mae_values.append(df.loc[(dataset, str(pred_len)), (model, module, "MAE")])
        
        if all_mse_values:
            df.loc[("Overall", "Mean"), (model, module, "MSE")] = np.mean(all_mse_values)
        if all_mae_values:
            df.loc[("Overall", "Mean"), (model, module, "MAE")] = np.mean(all_mae_values)

# Find best modules for each model, dataset, and pred_len
best_modules = {}

# For each index in the DataFrame (dataset, pred_len)
for idx in df.index:
    dataset, pred_len = idx
    
    # For each model
    for model in models:
        best_mse = float('inf')
        best_mse_module = None
        best_mae = float('inf')
        best_mae_module = None
        
        # Check each module
        for module in modules:
            # Check MSE
            if (model, module, "MSE") in df.columns and pd.notna(df.loc[idx, (model, module, "MSE")]):
                mse_val = df.loc[idx, (model, module, "MSE")]
                if mse_val < best_mse:
                    best_mse = mse_val
                    best_mse_module = module
            
            # Check MAE
            if (model, module, "MAE") in df.columns and pd.notna(df.loc[idx, (model, module, "MAE")]):
                mae_val = df.loc[idx, (model, module, "MAE")]
                if mae_val < best_mae:
                    best_mae = mae_val
                    best_mae_module = module
        
        # Store best modules
        if best_mse_module:
            best_modules[(dataset, pred_len, model, "MSE")] = best_mse_module
        if best_mae_module:
            best_modules[(dataset, pred_len, model, "MAE")] = best_mae_module

# Format the DataFrame to display numbers with 4 decimal places
for col in df.columns:
    df[col] = df[col].apply(lambda x: f"{x:.4f}" if pd.notna(x) else "-")

# Save results to CSV for basic compatibility
try:
    csv_path = os.path.join(output_dir, "ablation_results.csv")
    df.to_csv(csv_path)
    print(f"Results saved to {csv_path}")
except Exception as e:
    print(f"Could not save CSV file: {e}")

# Create a new custom formatted Excel file from scratch
try:
    # Create a new workbook with a single sheet
    wb = Workbook()
    ws = wb.active
    ws.title = "Ablation Results"
    
    # Write headers
    ws.cell(row=1, column=1, value="Dataset")
    ws.cell(row=1, column=2, value="Pred_Len")
    
    # Write model and module headers
    col = 3
    for model in models:
        # Merge cells for model name
        ws.merge_cells(start_row=1, start_column=col, end_row=1, end_column=col + len(modules)*2 - 1)
        cell = ws.cell(row=1, column=col, value=model)
        cell.font = Font(bold=True)
        
        # Write module headers
        module_col = col
        for module in modules:
            ws.merge_cells(start_row=2, start_column=module_col, end_row=2, end_column=module_col + 1)
            ws.cell(row=2, column=module_col, value=module.upper())
            
            # Write metric headers
            ws.cell(row=3, column=module_col, value="MSE")
            ws.cell(row=3, column=module_col + 1, value="MAE")
            
            module_col += 2
        
        col += len(modules) * 2
    
    # Define styles
    highlight_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")  # Yellow
    bold_font = Font(bold=True)
    
    # Write data
    row = 4
    for idx in df.index:
        dataset, pred_len = idx
        
        # Write dataset and pred_len
        ws.cell(row=row, column=1, value=dataset)
        ws.cell(row=row, column=2, value=pred_len)
        
        # Write data and highlight best modules
        col = 3
        for model in models:
            # Get best modules for this model, dataset, and pred_len
            best_mse_module = best_modules.get((dataset, pred_len, model, "MSE"))
            best_mae_module = best_modules.get((dataset, pred_len, model, "MAE"))
            
            # Write each module's results
            for module in modules:
                # Write MSE value
                mse_cell = ws.cell(row=row, column=col)
                try:
                    mse_val = df.loc[(dataset, pred_len), (model, module, "MSE")]
                    mse_cell.value = mse_val
                    
                    # Highlight if best
                    if module == best_mse_module:
                        mse_cell.fill = highlight_fill
                        mse_cell.font = bold_font
                except:
                    pass
                
                # Write MAE value
                mae_cell = ws.cell(row=row, column=col + 1)
                try:
                    mae_val = df.loc[(dataset, pred_len), (model, module, "MAE")]
                    mae_cell.value = mae_val
                    
                    # Highlight if best
                    if module == best_mae_module:
                        mae_cell.fill = highlight_fill
                        mae_cell.font = bold_font
                except:
                    pass
                
                # Move to next module
                col += 2
        
        # Move to next row
        row += 1
    
    # Auto-adjust column widths
    for col in range(1, ws.max_column + 1):
        column = get_column_letter(col)
        ws.column_dimensions[column].width = 12
    
    # Save the formatted workbook
    formatted_path = os.path.join(output_dir, "ablation_results_formatted.xlsx")
    wb.save(formatted_path)
    print(f"Custom formatted Excel file saved to {formatted_path}")
    
except Exception as e:
    print(f"Could not create custom Excel file: {e}")
    print(f"Error details: {str(e)}")

# Print summary of best performing modules for each model and dataset
print("\nBest Module Performance Summary:")
print("================================")

for dataset in datasets:
    print(f"\nDataset: {dataset}")
    print("-" * (len(dataset) + 10))
    
    for pred_len in pred_lengths:
        print(f"  Prediction Length: {pred_len}")
        
        for model in models:
            best_mse_module = best_modules.get((dataset, str(pred_len), model, "MSE"))
            
            if best_mse_module:
                val = df.loc[(dataset, str(pred_len)), (model, best_mse_module, "MSE")]
                if val != "-":
                    print(f"    {model}: Best module is {best_mse_module.upper()} (MSE: {val})")
        
        print("")  # Add spacing between prediction lengths
    
    # Print dataset means
    print(f"  Dataset Mean:")
    for model in models:
        best_mse_module = best_modules.get((dataset, "Mean", model, "MSE"))
        if best_mse_module:
            val = df.loc[(dataset, "Mean"), (model, best_mse_module, "MSE")]
            if val != "-":
                print(f"    {model}: Best module overall for {dataset} is {best_mse_module.upper()} (MSE: {val})")
    print("")

# Print overall best modules
print("\nOverall Best Modules:")
print("===================")
for model in models:
    best_overall_module = best_modules.get(("Overall", "Mean", model, "MSE"))
    if best_overall_module:
        val = df.loc[("Overall", "Mean"), (model, best_overall_module, "MSE")]
        if val != "-":
            print(f"{model}: Best module overall is {best_overall_module.upper()} (MSE: {val})")