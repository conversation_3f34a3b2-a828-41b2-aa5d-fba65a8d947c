import os
import re
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from matplotlib.gridspec import GridSpec

# Folders to process
hyperparams = ['32', '64', '128', '256', '512', '1024', '2048', '4096']
base_path = 'logs/ablation'

# Create results directory
results_dir = 'ablation_results'
os.makedirs(results_dir, exist_ok=True)

# Regular expression to extract MSE and MAE from log files
metrics_pattern = r'mse:([\d\.]+), mae:([\d\.]+)'

# Store results
results = []

# Process each folder and file
for hyperparam in hyperparams:
    folder_path = os.path.join(base_path, hyperparam)
    
    if not os.path.exists(folder_path):
        print(f"Warning: Folder {folder_path} does not exist. Skipping.")
        continue
    
    for filename in os.listdir(folder_path):
        if filename.endswith('.log'):
            # Parse filename to extract dataset and prediction length
            parts = filename.replace('.log', '').split('_')
            if len(parts) >= 4:
                dataset = parts[1]
                seq_len = parts[2]
                pred_len = parts[3]
                
                file_path = os.path.join(folder_path, filename)
                
                # Read the last few lines of the file to find metrics
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                        match = re.search(metrics_pattern, content)
                        if match:
                            mse = float(match.group(1))
                            mae = float(match.group(2))
                            
                            results.append({
                                'Hyperparameter': hyperparam,
                                'Dataset': dataset,
                                'SequenceLength': seq_len,
                                'PredictionLength': pred_len,
                                'MSE': mse,
                                'MAE': mae
                            })
                        else:
                            print(f"Could not find metrics in {file_path}")
                except Exception as e:
                    print(f"Error processing {file_path}: {e}")

# Convert to DataFrame
results_df = pd.DataFrame(results)

# Save original results to CSV
csv_path = os.path.join(results_dir, 'ablation_results.csv')
results_df.to_csv(csv_path, index=False)
print(f"Saved results to {csv_path}")

# Sort results by Hyperparameter, Dataset, and PredictionLength
# Convert columns to appropriate types for proper sorting
results_df['Hyperparameter'] = results_df['Hyperparameter'].astype(int)
results_df['PredictionLength'] = results_df['PredictionLength'].astype(int)

# Sort according to requirements
sorted_df = results_df.sort_values(by=['Hyperparameter', 'Dataset', 'PredictionLength'])

# Save sorted results to CSV
sorted_csv_path = os.path.join(results_dir, 'sorted_ablation_results.csv')
sorted_df.to_csv(sorted_csv_path, index=False)
print(f"Saved sorted results to {sorted_csv_path}")

# Create summary tables
if not results_df.empty:
    # Table 1: MSE by Dataset and Prediction Length, showing different hyperparameters
    pivot_mse = results_df.pivot_table(
        values='MSE', 
        index=['Dataset', 'PredictionLength'], 
        columns=['Hyperparameter'],
        aggfunc='mean'
    )
    
    # Table 2: MAE by Dataset and Prediction Length, showing different hyperparameters
    pivot_mae = results_df.pivot_table(
        values='MAE', 
        index=['Dataset', 'PredictionLength'], 
        columns=['Hyperparameter'],
        aggfunc='mean'
    )
    
    # Save pivot tables
    pivot_mse.to_csv(os.path.join(results_dir, 'mse_summary.csv'))
    pivot_mae.to_csv(os.path.join(results_dir, 'mae_summary.csv'))
    print(f"Saved summary tables to {results_dir}/mse_summary.csv and {results_dir}/mae_summary.csv")
    
    # Generate plots
    # 1. MSE plots for each dataset and prediction length, with hyperparameter as x-axis
    # for dataset in results_df['Dataset'].unique():
    #     dataset_df = results_df[results_df['Dataset'] == dataset]
        
    #     for pred_len in dataset_df['PredictionLength'].unique():
    #         plt.figure(figsize=(12, 6))
    #         subset = dataset_df[dataset_df['PredictionLength'] == pred_len]
            
    #         # Convert hyperparameter to numeric for proper ordering on x-axis
    #         subset = subset.copy()
    #         subset['Hyperparameter'] = subset['Hyperparameter'].astype(int)
            
    #         # Sort by hyperparameter for proper line plotting
    #         subset = subset.sort_values('Hyperparameter')
            
    #         plt.plot(subset['Hyperparameter'], subset['MSE'], marker='o')
            
    #         plt.title(f'MSE for {dataset} Dataset (Prediction Length: {pred_len})')
    #         plt.xlabel('Hidden Layer')
    #         plt.ylabel('MSE')
    #         plt.grid(True)
    #         plt.xscale('log', base=2)  # Using log scale for better visualization
    #         plt.xticks(subset['Hyperparameter'], subset['Hyperparameter'])
    #         plot_path = os.path.join(results_dir, f'mse_{dataset}_predlen_{pred_len}.png')
    #         plt.savefig(plot_path)
    #         plt.close()
    
    # 2. MAE plots for each dataset and prediction length, with hyperparameter as x-axis
    # for dataset in results_df['Dataset'].unique():
    #     dataset_df = results_df[results_df['Dataset'] == dataset]
        
    #     for pred_len in dataset_df['PredictionLength'].unique():
    #         plt.figure(figsize=(12, 6))
    #         subset = dataset_df[dataset_df['PredictionLength'] == pred_len]
            
    #         # Convert hyperparameter to numeric for proper ordering on x-axis
    #         subset = subset.copy()
    #         subset['Hyperparameter'] = subset['Hyperparameter'].astype(int)
            
    #         # Sort by hyperparameter for proper line plotting
    #         subset = subset.sort_values('Hyperparameter')
            
    #         plt.plot(subset['Hyperparameter'], subset['MAE'], marker='o')
            
    #         plt.title(f'MAE for {dataset} Dataset (Prediction Length: {pred_len})')
    #         plt.xlabel('Hidden Layer')
    #         plt.ylabel('MAE')
    #         plt.grid(True)
    #         plt.xscale('log', base=2)  # Using log scale for better visualization
    #         plt.xticks(subset['Hyperparameter'], subset['Hyperparameter'])
    #         plot_path = os.path.join(results_dir, f'mae_{dataset}_predlen_{pred_len}.png')
    #         plt.savefig(plot_path)
    #         plt.close()
    
    # 3. Heatmap for MSE across prediction lengths and hyperparameters for each dataset
    # for dataset in results_df['Dataset'].unique():
    #     dataset_df = results_df[results_df['Dataset'] == dataset]
        
    #     # Pivot the data for the heatmap with hyperparameters as columns
    #     heatmap_data = dataset_df.pivot_table(
    #         values='MSE', 
    #         index='PredictionLength',
    #         columns='Hyperparameter',
    #         aggfunc='mean'
    #     )
        
    #     plt.figure(figsize=(10, 6))
    #     sns.heatmap(heatmap_data, annot=True, cmap='YlGnBu', fmt='.4f')
    #     plt.title(f'MSE Heatmap for {dataset}')
    #     plt.xlabel('Hyperparameter')
    #     plt.ylabel('Prediction Length')
    #     plt.tight_layout()
    #     plot_path = os.path.join(results_dir, f'heatmap_mse_{dataset}.png')
    #     plt.savefig(plot_path)
    #     plt.close()
    
    # 4. Calculate and plot average metrics across all prediction lengths for each dataset
    # # Calculate average MSE for each dataset and hyperparameter
    # avg_by_dataset_mse = results_df.groupby(['Dataset', 'Hyperparameter'])['MSE'].mean().reset_index()
    # avg_by_dataset_mae = results_df.groupby(['Dataset', 'Hyperparameter'])['MAE'].mean().reset_index()
    
    # # Save to CSV
    # avg_by_dataset_mse.to_csv(os.path.join(results_dir, 'avg_mse_by_dataset.csv'), index=False)
    # avg_by_dataset_mae.to_csv(os.path.join(results_dir, 'avg_mae_by_dataset.csv'), index=False)
    # print(f"Saved average metrics to {results_dir}/avg_mse_by_dataset.csv and {results_dir}/avg_mae_by_dataset.csv")
    
    # # Plot average MSE for each dataset
    # for dataset in avg_by_dataset_mse['Dataset'].unique():
    #     plt.figure(figsize=(12, 6))
    #     subset = avg_by_dataset_mse[avg_by_dataset_mse['Dataset'] == dataset]
        
    #     # Convert hyperparameter to numeric for proper ordering on x-axis
    #     subset = subset.copy()
    #     subset['Hyperparameter'] = subset['Hyperparameter'].astype(int)
        
    #     # Sort by hyperparameter for proper line plotting
    #     subset = subset.sort_values('Hyperparameter')
        
    #     plt.plot(subset['Hyperparameter'], subset['MSE'], marker='o')
        
    #     plt.title(f'Average MSE across all Prediction Lengths for {dataset} Dataset')
    #     plt.xlabel('Hidden Layer')
    #     plt.ylabel('Average MSE')
    #     plt.grid(True)
    #     plt.xscale('log', base=2)  # Using log scale for better visualization
    #     plt.xticks(subset['Hyperparameter'], subset['Hyperparameter'])
    #     plot_path = os.path.join(results_dir, f'avg_mse_{dataset}.png')
    #     plt.savefig(plot_path)
    #     plt.close()
    
    # # Plot average MAE for each dataset
    # for dataset in avg_by_dataset_mae['Dataset'].unique():
    #     plt.figure(figsize=(12, 6))
    #     subset = avg_by_dataset_mae[avg_by_dataset_mae['Dataset'] == dataset]
        
    #     # Convert hyperparameter to numeric for proper ordering on x-axis
    #     subset = subset.copy()
    #     subset['Hyperparameter'] = subset['Hyperparameter'].astype(int)
        
    #     # Sort by hyperparameter for proper line plotting
    #     subset = subset.sort_values('Hyperparameter')
        
    #     plt.plot(subset['Hyperparameter'], subset['MAE'], marker='o')
        
    #     plt.title(f'Average MAE across all Prediction Lengths for {dataset} Dataset')
    #     plt.xlabel('Hidden Layer')
    #     plt.ylabel('Average MAE')
    #     plt.grid(True)
    #     plt.xscale('log', base=2)  # Using log scale for better visualization
    #     plt.xticks(subset['Hyperparameter'], subset['Hyperparameter'])
    #     plot_path = os.path.join(results_dir, f'avg_mae_{dataset}.png')
    #     plt.savefig(plot_path)
    #     plt.close()
    
    # 5. Create a 6-subplot figure: left side MSE, right side MAE for each dataset
    # Filter out weather dataset and specify the exact order of datasets
    filtered_datasets = ['ETTh1', 'solar', 'traffic']
    
    # Calculate average over all prediction lengths
    avg_by_dataset = results_df.groupby(['Dataset', 'Hyperparameter']).agg({
        'MSE': 'mean',
        'MAE': 'mean'
    }).reset_index()
    
    # Filter for the specific datasets in the desired order
    avg_by_dataset = avg_by_dataset[avg_by_dataset['Dataset'].isin(filtered_datasets)]
    
    # Create figure with 3 rows, 2 columns
    fig, axes = plt.subplots(nrows=3, ncols=2, figsize=(20, 15))
    
    # Define markers for different hyperparameters
    markers = ['o', 's', 'D', '^', 'v', '<', '>', 'p', '*', 'h', 'H', '+', 'x', 'd', '|', '_']
    
    # Plot MSE (left column) and MAE (right column) for each dataset in the specified order
    for i, dataset in enumerate(filtered_datasets):
        # Get dataset data if it exists in the results
        if dataset in avg_by_dataset['Dataset'].values:
            subset = avg_by_dataset[avg_by_dataset['Dataset'] == dataset].copy()
            subset['Hyperparameter'] = subset['Hyperparameter'].astype(int)
            subset = subset.sort_values('Hyperparameter')
            
            # Plot MSE on the left
            axes[i, 0].plot(subset['Hyperparameter'], subset['MSE'], marker='o', linestyle='-', 
                             linewidth=2, color='tab:blue')
            axes[i, 0].set_title(f'MSE for {dataset} Dataset', fontsize=14)
            axes[i, 0].set_xlabel('Hidden Layer Size', fontsize=12)
            axes[i, 0].set_ylabel('Average MSE', fontsize=12)
            axes[i, 0].grid(True)
            axes[i, 0].set_xscale('log', base=2)
            axes[i, 0].set_xticks(subset['Hyperparameter'])
            axes[i, 0].set_xticklabels(subset['Hyperparameter'], rotation=45)
            
            # Plot MAE on the right
            axes[i, 1].plot(subset['Hyperparameter'], subset['MAE'], marker='s', linestyle='-',
                             linewidth=2, color='tab:red')
            axes[i, 1].set_title(f'MAE for {dataset} Dataset', fontsize=14)
            axes[i, 1].set_xlabel('Hidden Layer Size', fontsize=12)
            axes[i, 1].set_ylabel('Average MAE', fontsize=12)
            axes[i, 1].grid(True)
            axes[i, 1].set_xscale('log', base=2)
            axes[i, 1].set_xticks(subset['Hyperparameter'])
            axes[i, 1].set_xticklabels(subset['Hyperparameter'], rotation=45)
        else:
            # Dataset not found in results
            axes[i, 0].text(0.5, 0.5, f"No data available for {dataset}", 
                           horizontalalignment='center', verticalalignment='center',
                           transform=axes[i, 0].transAxes, fontsize=14)
            axes[i, 1].text(0.5, 0.5, f"No data available for {dataset}", 
                           horizontalalignment='center', verticalalignment='center',
                           transform=axes[i, 1].transAxes, fontsize=14)
    
    plt.tight_layout()
    plt_path = os.path.join(results_dir, 'mse_mae_comparison.png')
    plt.savefig(plt_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Generated MSE/MAE comparison for datasets in {results_dir}/mse_mae_comparison.png")
    print(f"Generated plots for each dataset with hyperparameters on x-axis in {results_dir} folder")
else:
    print("No results to analyze") 