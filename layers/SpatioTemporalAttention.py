import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np

class SpatioTemporalAttention(nn.Module):
    """
    时空注意力机制，同时考虑时间和特征之间的关系
    """
    def __init__(self, d_model, n_heads=8, dropout=0.1):
        super(SpatioTemporalAttention, self).__init__()
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads
        self.scale = self.d_k ** -0.5
        
        # 查询、键、值投影
        self.q_proj = nn.Linear(d_model, d_model)
        self.k_proj = nn.Linear(d_model, d_model)
        self.v_proj = nn.Linear(d_model, d_model)
        self.out_proj = nn.Linear(d_model, d_model)
        
        # 时间注意力和特征注意力的权重
        self.temporal_weight = nn.Parameter(torch.ones(1))
        self.spatial_weight = nn.Parameter(torch.ones(1))
        
        # Dropout
        self.attn_dropout = nn.Dropout(dropout)
        self.proj_dropout = nn.Dropout(dropout)
        
        # 局部感知卷积
        self.local_enhance = nn.Sequential(
            nn.Conv2d(n_heads, n_heads, kernel_size=3, padding=1, groups=n_heads),
            nn.LeakyReLU()
        )
        
        # 稀疏注意力的Top-k选择
        self.topk_ratio = 0.5  # 默认只保留50%的注意力连接
        
    def _reshape_to_batches(self, x):
        batch_size, seq_len, _ = x.size()
        x = x.reshape(batch_size, seq_len, self.n_heads, self.d_k)
        x = x.permute(0, 2, 1, 3)  # [B, H, L, d_k]
        return x
    
    def _reshape_from_batches(self, x):
        batch_size, n_heads, seq_len, d_k = x.size()
        x = x.permute(0, 2, 1, 3)  # [B, L, H, d_k]
        x = x.reshape(batch_size, seq_len, self.d_model)
        return x
    
    def _apply_sparse_mask(self, attn_weights):
        """应用稀疏掩码，只保留top-k个注意力权重"""
        batch_size, n_heads, seq_len, _ = attn_weights.size()
        
        # 计算每行应保留的注意力连接数
        k = max(1, int(seq_len * self.topk_ratio))
        
        # 获取top-k个值的掩码
        topk_values, _ = torch.topk(attn_weights, k, dim=-1)
        threshold = topk_values[:, :, :, -1].unsqueeze(-1)
        
        # 创建掩码，小于阈值的设为负无穷
        sparse_mask = attn_weights < threshold
        return attn_weights.masked_fill(sparse_mask, -float('inf'))
    
    def forward(self, x, mask=None):
        """
        Args:
            x: 输入张量 [batch_size, seq_len, d_model]
            mask: 注意力掩码，可选
        Returns:
            output: 增强后的特征 [batch_size, seq_len, d_model]
            attn_weights: 注意力权重，用于可视化
        """
        batch_size, seq_len, _ = x.size()
        
        # 1. 投影查询、键、值
        q = self._reshape_to_batches(self.q_proj(x))  # [B, H, L, d_k]
        k = self._reshape_to_batches(self.k_proj(x))  # [B, H, L, d_k]
        v = self._reshape_to_batches(self.v_proj(x))  # [B, H, L, d_k]
        
        # 2. 计算时间注意力（沿序列维度）
        temporal_attn = torch.matmul(q, k.transpose(-2, -1)) * self.scale  # [B, H, L, L]
        
        # 应用掩码（如果提供）
        if mask is not None:
            temporal_attn = temporal_attn.masked_fill(mask == 0, -1e9)
        
        # 应用稀疏注意力
        temporal_attn = self._apply_sparse_mask(temporal_attn)
        
        # Softmax和dropout
        temporal_attn = F.softmax(temporal_attn, dim=-1)
        temporal_attn = self.attn_dropout(temporal_attn)
        
        # 增强局部感知
        temporal_attn = temporal_attn.permute(0, 1, 3, 2)  # [B, H, L, L]
        temporal_attn = self.local_enhance(temporal_attn.permute(0, 1, 3, 2)).permute(0, 1, 3, 2)
        
        # 应用注意力
        temporal_output = torch.matmul(temporal_attn, v)  # [B, H, L, d_k]
        
        # 3. 计算特征注意力（跨通道）
        # 转置k和v以在特征维度上计算注意力
        k_t = k.permute(0, 1, 3, 2)  # [B, H, d_k, L]
        spatial_attn = torch.matmul(q, k_t) * self.scale  # [B, H, L, L]
        
        # Softmax和dropout
        spatial_attn = F.softmax(spatial_attn, dim=-1)
        spatial_attn = self.attn_dropout(spatial_attn)
        
        # 应用特征注意力
        v_t = v.permute(0, 1, 3, 2)  # [B, H, d_k, L]
        spatial_output = torch.matmul(spatial_attn, v_t.permute(0, 1, 3, 2))  # [B, H, L, d_k]
        
        # 4. 融合时间和特征注意力输出
        # 使用可学习权重融合两种注意力
        w_t = F.sigmoid(self.temporal_weight)
        w_s = F.sigmoid(self.spatial_weight)
        
        # 归一化权重
        sum_weights = w_t + w_s
        w_t = w_t / sum_weights
        w_s = w_s / sum_weights
        
        combined_output = w_t * temporal_output + w_s * spatial_output
        
        # 5. 转回原始形状
        output = self._reshape_from_batches(combined_output)
        
        # 6. 线性投影和dropout
        output = self.out_proj(output)
        output = self.proj_dropout(output)
        
        return output, temporal_attn


class SpatioTemporalEnhancementBlock(nn.Module):
    """
    时空增强模块，结合了注意力机制和前馈网络
    """
    def __init__(self, d_model, n_heads=8, dim_feedforward=2048, dropout=0.1):
        super(SpatioTemporalEnhancementBlock, self).__init__()
        
        # 时空注意力机制
        self.attn = SpatioTemporalAttention(d_model, n_heads, dropout)
        
        # 层归一化
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        # 前馈网络
        self.ffn = nn.Sequential(
            nn.Linear(d_model, dim_feedforward),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(dim_feedforward, d_model),
            nn.Dropout(dropout)
        )
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, mask=None):
        """
        Args:
            x: 输入张量 [batch_size, seq_len, d_model]
            mask: 注意力掩码，可选
        Returns:
            output: 增强后的特征 [batch_size, seq_len, d_model]
        """
        # 注意力子层
        attn_output, attn_weights = self.attn(x, mask)
        x = x + self.dropout(attn_output)
        x = self.norm1(x)
        
        # 前馈子层
        ffn_output = self.ffn(x)
        x = x + ffn_output
        x = self.norm2(x)
        
        return x, attn_weights 