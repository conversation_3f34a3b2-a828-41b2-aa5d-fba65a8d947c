import torch
import torch.nn as nn
import torch.nn.functional as F
from layers.revin import RevIN
import math

# 在fre_network上改进


class FreNetwork(nn.Module):
    # 未加残差部分的
    def __init__(self, configs):
        super(FreNetwork, self).__init__()
        # 配置参数
        self.embed_size = configs.FreMLP_embed_size  # 嵌入维度   4096
        self.hidden_size = configs.FreMLP_hidden_size  # 隐藏层大小  4096
        self.pre_length = configs.pred_len  # 预测长度
        self.channels = configs.enc_in  # 特征大小/通道数
        self.seq_length = configs.seq_len  # 序列长度
        self.type = configs.model_type


        self.sparsity_threshold = 0.01  # 稀疏阈值
        # 初始化参数
        self.scale = 0.02

        self.embeddings = nn.Parameter(torch.randn(1, self.embed_size))

        # 频域MLP参数 - 第一组
        self.r1 = nn.Parameter(self.scale * torch.randn(self.embed_size, self.embed_size))
        self.i1 = nn.Parameter(self.scale * torch.randn(self.embed_size, self.embed_size))
        self.rb1 = nn.Parameter(self.scale * torch.randn(self.embed_size))
        self.ib1 = nn.Parameter(self.scale * torch.randn(self.embed_size))


        # cof = math.floor((math.log(self.channels)**2 - 4 * math.log(self.channels) + 10) / 4)
        cof = math.ceil((math.sqrt(self.channels))/5)

        if self.type == 'mlp':
            self.Seasonal = nn.Sequential(
                nn.Linear(self.seq_length, 2*self.seq_length*cof),
                nn.Tanh(),
                nn.Dropout(0.4),
                nn.Linear(2*self.seq_length*cof, self.pre_length)
            )
            # self.Seasonal = nn.Sequential(
            #     nn.Linear(self.seq_length, 4096),
            #     nn.Tanh(),
            #     nn.Dropout(0.4),
            #     nn.Linear(4096, self.pre_length)
            # )

        elif self.type == 'linear':
            self.Seasonal = nn.Sequential(
                nn.Linear(self.seq_length, self.pre_length)
            )
        else:
            raise ValueError(f"Invalid model type: {self.type}")

        # 全连接输出层
        if self.type == 'mlp':
            self.fc = nn.Sequential(
                nn.Linear(self.seq_length * self.embed_size, self.hidden_size*cof),
                nn.LeakyReLU(),
                nn.Dropout(0.4),
                nn.Linear(self.hidden_size*cof, self.pre_length)
            )
            # self.fc = nn.Sequential(
            #     nn.Linear(self.seq_length * self.embed_size, 4096),
            #     nn.LeakyReLU(),
            #     nn.Dropout(0.4),
            #     nn.Linear(4096, self.pre_length)
            # )
            
        elif self.type == 'linear':
            self.fc = nn.Sequential(
                nn.Linear(self.seq_length * self.embed_size, self.pre_length)
            )
        else:
            raise ValueError(f"Invalid model type: {self.type}")    

        # self.alpha = nn.Parameter(torch.zeros(self.channels))  # 每个通道一个权重
        self.alpha = nn.Parameter(torch.randn(self.channels))  # 每个通道一个权重
        self.beta = nn.Parameter(torch.zeros(self.channels))  # 每个通道一个权重

        # # 融合MLP
        # self.fusion_mlp = nn.Sequential(
        #     nn.Linear(self.channels * 2, self.channels * 4),
        #     nn.LeakyReLU(),
        #     nn.Dropout(0.3),
        #     nn.Linear(self.channels * 4, self.channels)
        # )

        self.attention_mlp = nn.Sequential(
            nn.Linear(self.channels * 2, self.channels),
            nn.ReLU(),
            nn.Linear(self.channels, self.channels),
            nn.Sigmoid()
        )

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """更好的权重初始化方法"""
        nn.init.normal_(self.embeddings, mean=0, std=0.02)
        for layer in self.fc:
            if isinstance(layer, nn.Linear):
                nn.init.xavier_uniform_(layer.weight)
                if layer.bias is not None:
                    nn.init.zeros_(layer.bias)
        # for layer in self.fusion_mlp:
        #     if isinstance(layer, nn.Linear):
        #         nn.init.xavier_uniform_(layer.weight)
        #         if layer.bias is not None:
        #             nn.init.zeros_(layer.bias)

    def tokenEmb(self, x):
        """
        维度扩展，将输入嵌入到高维空间

        Args:
            x: [Batch, Input length, Channel]
        Returns:
            嵌入后的张量 [Batch, Channel, Input length, Embed_size]
        """
        # 维度转换: [B, T, N] -> [B, N, T, 1]
        x = x.permute(0, 2, 1)
        x = x.unsqueeze(3)
        # 嵌入操作
        return x * self.embeddings


    def FreMLP(self, B, nd, dimension, x, r1, i1, rb1, ib1):
        """
        频域MLP操作

        Args:
            B: 批次大小
            nd: 维度数
            dimension: FFT维度
            x: 复数张量（PyTorch 1.7+）或按[..., 0:实部, 1:虚部]排列的旧格式张量
            r, i, rb, ib: 权重和偏置
        Returns:
            复数MLP输出
        """


        o1_real = F.tanh(torch.einsum('bijd,dd->bijd', x.real, r1) - \
            torch.einsum('bijd,dd->bijd', x.imag, i1) + \
            rb1)


        o1_imag = F.tanh(torch.einsum('bijd,dd->bijd', x.imag, r1) + \
            torch.einsum('bijd,dd->bijd', x.real, i1) + \
            ib1)


        stacked = torch.stack([o1_real, o1_imag], dim=-1)

        y_shrink = F.softshrink(stacked, lambd=self.sparsity_threshold)  # self.sparsity_threshold, adaptive_threshold

        y = torch.view_as_complex(y_shrink)

        return y

    def MLP_temporal(self, x, B, N, L):

        # [B, N, T, D]
        x = torch.fft.rfft(x, dim=2, norm='ortho') # FFT on L dimension
        y = self.FreMLP(B, N, L, x, self.r1, self.i1, self.rb1, self.ib1)
        x = torch.fft.irfft(y, n=self.seq_length, dim=2, norm="ortho")

        return x



    def forward(self, x):
        """
        前向传播

        Args:
            x: 输入张量 [Batch, Input length, Channel]
        Returns:
            输出张量 [Batch, Pred length, Channel]
        """
        # 提取形状信息
        B, T, N = x.shape


        # 嵌入: [B, N, T, D], 周期部分频域处理
        x1 = self.tokenEmb(x)
        x1 = self.MLP_temporal(x1, B, N, T)   # [batch, channel, Seq, Emb]
        x1 = self.fc(x1.reshape(B, N, -1).contiguous()).permute(0, 2, 1)   # [batch, channel, Seq*Emb]

        # 残差时序部分
        x2 = self.Seasonal(x.permute(0, 2, 1)).permute(0, 2, 1)
        
        # 1.1 去掉x2
        # out = x1

        # 1.2 MLP融合
        # # 拼接特征并通过MLP进行融合
        # concat_features = torch.cat([x1, x2], dim=2)  # [B, pred_len, N*2]
        # # 直接在时间维度上应用MLP
        # B, pred_len, N_doubled = concat_features.shape
        # # 重塑为 [B*pred_len, N*2] 以便一次性处理所有时间步
        # reshaped_features = concat_features.reshape(-1, N_doubled)
        # # 应用融合MLP
        # fused_features = self.fusion_mlp(reshaped_features)  # [B*pred_len, N]
        # # 重塑回 [B, pred_len, N]
        # out = fused_features.reshape(B, pred_len, N)

        # 1.3 双权重融合
        # alpha = self.alpha.view(1, 1, -1).contiguous()  # [1, 1, N]
        # beta = self.beta.view(1, 1, -1).contiguous()
        # out = beta * x1 + alpha * x2

        # 1.4 通道注意力融合
        w = self.attention_mlp(torch.cat([x1, x2], dim=-1))
        out = w * x1 + (1 - w) * x2 

        # 1. 权重融合
        # alpha = self.alpha.view(1, 1, -1).contiguous()  # [1, 1, N]
        # out = x1 + alpha * x2

        return out

