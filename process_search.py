import re
import os
from collections import defaultdict

def parse_results(file_path):
    """Parse the results from the given file."""
    results = []
    
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    i = 0
    while i < len(lines):
        if lines[i].strip():  # Skip empty lines
            # Parse model ID line
            model_line = lines[i].strip()
            
            # Extract dataset, pred_len, seq_len using regex
            pattern = r'([A-Za-z0-9]+)_(\d+)_ema_MyModel_[A-Za-z0-9]+_ftM_sl(\d+)_ll\d+_pl\d+'
            match = re.search(pattern, model_line)
            
            if match:
                dataset = match.group(1)
                pred_len = int(match.group(2))
                seq_len = int(match.group(3))
                
                # Get metrics from next line
                if i + 1 < len(lines) and "mse:" in lines[i + 1]:
                    metrics_line = lines[i + 1].strip()
                    
                    # Extract MSE and MAE values
                    mse_match = re.search(r'mse:([0-9.]+)', metrics_line)
                    mae_match = re.search(r'mae:([0-9.]+)', metrics_line)
                    
                    if mse_match and mae_match:
                        mse = float(mse_match.group(1))
                        mae = float(mae_match.group(1))
                        
                        results.append({
                            'dataset': dataset,
                            'pred_len': pred_len,
                            'seq_len': seq_len,
                            'mse': mse,
                            'mae': mae
                        })
            
            # Skip to next result (accounting for metrics line and possible empty line)
            i += 2
        else:
            i += 1
    
    return results

def organize_results(results):
    """Organize results by dataset and prediction length."""
    organized = defaultdict(lambda: defaultdict(list))
    
    for result in results:
        dataset = result['dataset']
        pred_len = result['pred_len']
        organized[dataset][pred_len].append(result)
    
    # Sort by sequence length within each group
    for dataset in organized:
        for pred_len in organized[dataset]:
            organized[dataset][pred_len].sort(key=lambda x: x['seq_len'])
    
    return organized

def find_best_results(organized_results):
    """Find the best sequence length for each dataset and prediction length based on MSE."""
    best_results = {}
    
    for dataset in organized_results:
        best_results[dataset] = {}
        
        for pred_len in organized_results[dataset]:
            # Find result with minimum MSE
            results = organized_results[dataset][pred_len]
            best_result = min(results, key=lambda x: x['mse'])
            
            best_results[dataset][pred_len] = best_result
    
    return best_results

def save_results(organized_results, best_results, output_file):
    """Save organized and best results to a text file."""
    with open(output_file, 'w') as f:
        f.write("=" * 80 + "\n")
        f.write("ORGANIZED RESULTS BY DATASET, PREDICTION LENGTH, AND SEQUENCE LENGTH\n")
        f.write("=" * 80 + "\n\n")
        
        for dataset in sorted(organized_results.keys()):
            f.write(f"DATASET: {dataset}\n")
            f.write("-" * 80 + "\n")
            
            for pred_len in sorted(organized_results[dataset].keys()):
                f.write(f"\nPrediction Length: {pred_len}\n")
                f.write("-" * 40 + "\n")
                
                f.write(f"{'Seq Length':<12}{'MSE':<15}{'MAE':<15}\n")
                for result in organized_results[dataset][pred_len]:
                    f.write(f"{result['seq_len']:<12}{result['mse']:<15.6f}{result['mae']:<15.6f}\n")
            
            f.write("\n\n")
        
        f.write("=" * 80 + "\n")
        f.write("BEST INPUT SEQUENCE LENGTH FOR EACH DATASET AND PREDICTION LENGTH\n")
        f.write("=" * 80 + "\n\n")
        
        for dataset in sorted(best_results.keys()):
            f.write(f"DATASET: {dataset}\n")
            f.write("-" * 80 + "\n")
            
            f.write(f"{'Pred Length':<12}{'Best Seq Len':<15}{'MSE':<15}{'MAE':<15}\n")
            for pred_len in sorted(best_results[dataset].keys()):
                result = best_results[dataset][pred_len]
                f.write(f"{pred_len:<12}{result['seq_len']:<15}{result['mse']:<15.6f}{result['mae']:<15.6f}\n")
            
            f.write("\n\n")

def main():
    input_file = "result_search.txt"
    output_file = "optimized_sequence_lengths.txt"
    
    # Parse results from file
    results = parse_results(input_file)
    
    # Organize results by dataset and prediction length
    organized_results = organize_results(results)
    
    # Find best sequence length for each dataset and prediction length
    best_results = find_best_results(organized_results)
    
    # Save organized and best results to file
    save_results(organized_results, best_results, output_file)
    
    print(f"Results successfully organized and saved to {output_file}")

if __name__ == "__main__":
    main()