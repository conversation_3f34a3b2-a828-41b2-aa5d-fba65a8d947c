import argparse
import os
import torch
import numpy as np
import time
from exp.exp_main import Exp_Main
from exp.exp_CARD_long_term_forecasting import Exp_Long_Term_Forecast as Exp_CARD_Long_Term_Forecast
from utils.tools import visual
from utils.metrics import metric

def test_model(args):
    """
    专门用于测试已经训练好的模型，正确处理不同的检查点路径
    """
    print('Test Arguments:')
    print(args)
    
    # 兼容性代码 - 确保args.revin存在
    if hasattr(args, 'use_revin') and not hasattr(args, 'revin'):
        args.revin = args.use_revin
    
    # 设置成测试模式
    args.is_training = 0
    
    # 选择实验类
    if args.model == 'CARD':
        Exp = Exp_CARD_Long_Term_Forecast
    else:
        Exp = Exp_Main

    # 创建实验实例
    exp = Exp(args)
    
    # 构建实验设置名称
    setting = '{}_{}_{}_ft{}_sl{}_ll{}_pl{}_{}'.format(
        args.model_id,
        args.model,
        args.data,
        args.features,
        args.seq_len,
        args.label_len,
        args.pred_len,
        'test')
    
    # 创建结果目录
    test_results_path = os.path.join('./test_results/', setting)
    if not os.path.exists(test_results_path):
        os.makedirs(test_results_path)
        
    # 优先从指定的检查点目录加载模型
    model_loaded = False
    
    # 构建检查点路径
    checkpoint_dir = args.checkpoints  # 例如 "./checkpoints_best/"
    
    # 尝试不同可能的文件名和路径
    model_paths = [
        os.path.join(checkpoint_dir, setting, 'best_model.pth'),
        os.path.join(checkpoint_dir, setting, 'checkpoint.pth'),
        os.path.join(checkpoint_dir, args.model_id, 'best_model.pth'),
        os.path.join(checkpoint_dir, args.model_id, 'checkpoint.pth')
    ]
    
    # 尝试加载模型
    for model_path in model_paths:
        if os.path.exists(model_path):
            print(f"找到模型文件: {model_path}")
            try:
                exp.model.load_state_dict(torch.load(model_path))
                print(f"成功从 {model_path} 加载模型")
                model_loaded = True
                break
            except Exception as e:
                print(f"加载 {model_path} 时出错: {e}")
    
    if not model_loaded:
        print("警告: 找不到有效的模型文件。请检查checkpoints路径和model_id是否正确。")
        print(f"已尝试的路径: {model_paths}")
        return
    
    # 获取测试数据
    test_data, test_loader = exp._get_data(flag='test')
    
    # 开始测试
    print(f'>>>>>>>开始测试: {setting}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<')
    
    preds = []
    trues = []
    
    # 存储测试过程中的前向传播时间
    test_forward_times = []
    
    exp.model.eval()
    with torch.no_grad():
        for i, (batch_x, batch_y, batch_x_mark, batch_y_mark, batch_cycle) in enumerate(test_loader):
            batch_x = batch_x.float().to(exp.device)
            batch_y = batch_y.float().to(exp.device)
            batch_x_mark = batch_x_mark.float().to(exp.device)
            batch_y_mark = batch_y_mark.float().to(exp.device)
            batch_cycle = batch_cycle.int().to(exp.device)

            # decoder input
            dec_inp = torch.zeros_like(batch_y[:, -args.pred_len:, :]).float()
            dec_inp = torch.cat([batch_y[:, :args.label_len, :], dec_inp], dim=1).float().to(exp.device)
            
            # 计时器开始记录前向传播时间
            forward_start = time.time()
            
            # 使用正确的前向传播逻辑
            list_A = {'Linear', 'MLP', 'SegRNN', 'TST', 'SparseTSF', 'xPatch', 'DLinear', 'MyModel', 'Amplifier', 'PaiFilter', 'TexFilter', 'SparseTSF', 'FreTS', 'FITS'}
            
            # encoder - decoder
            if args.use_amp:
                with torch.cuda.amp.autocast():
                    if any(substr in args.model for substr in {'Cycle'}):
                        outputs = exp.model(batch_x, batch_cycle)
                    elif any(substr in args.model for substr in list_A):
                        outputs = exp.model(batch_x)
                    else:
                        if args.output_attention:
                            outputs = exp.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)[0]
                        else:
                            outputs = exp.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)
            else:
                if any(substr in args.model for substr in {'Cycle'}):
                    outputs = exp.model(batch_x, batch_cycle)
                elif any(substr in args.model for substr in list_A):
                    outputs = exp.model(batch_x)
                else:
                    if args.output_attention:
                        outputs = exp.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)[0]
                    else:
                        outputs = exp.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)
            
            # 计时器结束记录前向传播时间
            forward_time = time.time() - forward_start
            test_forward_times.append(forward_time)

            f_dim = -1 if args.features == 'MS' else 0
            outputs = outputs[:, -args.pred_len:, f_dim:]
            batch_y = batch_y[:, -args.pred_len:, f_dim:].to(exp.device)
            outputs = outputs.detach().cpu().numpy()
            batch_y = batch_y.detach().cpu().numpy()

            pred = outputs
            true = batch_y

            preds.append(pred)
            trues.append(true)

            # 可视化结果
            if i % 20 == 0 and args.plot_result:
                input = batch_x.detach().cpu().numpy()
                gt = np.concatenate((input[0, :, -1], true[0, :, -1]), axis=0)
                pd = np.concatenate((input[0, :, -1], pred[0, :, -1]), axis=0)
                visual(gt, pd, os.path.join(test_results_path, str(i) + '.pdf'))
        
    # 计算并打印测试过程的平均前向传播时间
    avg_test_forward_time = np.mean(test_forward_times) if test_forward_times else 0
    print(f"测试推理时间 (仅前向传播): {avg_test_forward_time:.6f}s 每批次")
    print(f"总测试前向传播时间: {sum(test_forward_times):.6f}s")
    
    # 合并预测结果和真实值
    preds = np.concatenate(preds, axis=0)
    trues = np.concatenate(trues, axis=0)

    preds = preds.reshape(-1, preds.shape[-2], preds.shape[-1])
    trues = trues.reshape(-1, trues.shape[-2], trues.shape[-1])

    # 计算指标
    mae, mse = metric(preds, trues)
    print('MSE: {}, MAE: {}'.format(mse, mae))
    
    # 保存结果
    f = open(args.result_file, 'a')
    f.write(setting + "  \n")
    f.write('MSE: {}, MAE: {}'.format(mse, mae))
    f.write('\n')
    f.write(f'平均推理时间: {avg_test_forward_time:.6f}s 每批次')
    f.write('\n')
    f.write('\n')
    f.close()
    
    # 如果需要，保存预测结果
    if args.save_prediction:
        folder_path = os.path.join('./results/', setting, '')
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)
        np.save(os.path.join(folder_path, 'predictions.npy'), preds)
        np.save(os.path.join(folder_path, 'trues.npy'), trues)
        print(f"预测结果已保存到 {folder_path}")
    
    print(f"测试完成。结果已保存到 {args.result_file}")
    
    return mse, mae

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='模型测试脚本')
    
    # 基本配置
    parser.add_argument('--model_id', type=str, required=True, help='模型ID')
    parser.add_argument('--model', type=str, required=True, help='模型名称')
    parser.add_argument('--checkpoints', type=str, default='./checkpoints_best/', help='模型检查点位置')
    
    # 数据加载器
    parser.add_argument('--data', type=str, required=True, help='数据集类型')
    parser.add_argument('--root_path', type=str, default='./dataset', help='数据文件根路径')
    parser.add_argument('--data_path', type=str, default='ETTh1.csv', help='数据文件')
    parser.add_argument('--features', type=str, default='M', help='预测任务类型:[M, S, MS]')
    parser.add_argument('--target', type=str, default='OT', help='S或MS任务中的目标特征')
    parser.add_argument('--freq', type=str, default='h', help='时间特征编码的频率')
    
    # 预测任务
    parser.add_argument('--seq_len', type=int, default=96, help='输入序列长度')
    parser.add_argument('--label_len', type=int, default=48, help='开始标记长度')
    parser.add_argument('--pred_len', type=int, default=96, help='预测序列长度')
    
    # Patching
    parser.add_argument('--patch_len', type=int, default=16, help='patch长度')
    parser.add_argument('--stride', type=int, default=8, help='步幅')
    parser.add_argument('--padding_patch', default='end', help='None: 无填充; end: 在末尾填充')
    
    # Moving Average
    parser.add_argument('--ma_type', type=str, default='ema', help='reg, ema, dema')
    parser.add_argument('--alpha', type=float, default=0.3, help='alpha参数')
    parser.add_argument('--beta', type=float, default=0.3, help='beta参数')
    
    # 模型特定参数
    parser.add_argument('--FreMLP_embed_size', type=int, default=64, help='FreMLP嵌入大小')
    parser.add_argument('--FreMLP_hidden_size', type=int, default=256, help='FreMLP隐藏层大小')
    parser.add_argument('--individual', action='store_true', default=False, help='DLinear: 单独处理每个通道')
    parser.add_argument('--cycle', type=int, default=24, help='周期长度')
    parser.add_argument('--model_type', type=str, default='mlp', help='模型类型: [linear, mlp]')
    parser.add_argument('--use_revin', type=int, default=1, help='1: 使用revin, 0: 不使用revin')
    parser.add_argument('--embed_size', default=128, type=int, help='嵌入大小')
    parser.add_argument('--period_len', type=int, default=24, help='周期长度')
    
    # 数据处理参数
    parser.add_argument('--enc_in', type=int, default=7, help='编码器输入大小')
    parser.add_argument('--dec_in', type=int, default=7, help='解码器输入大小')
    parser.add_argument('--c_out', type=int, default=7, help='输出大小')
    parser.add_argument('--batch_size', type=int, default=32, help='测试数据批量大小')
    parser.add_argument('--num_workers', type=int, default=0, help='数据加载器工作线程数')
    
    # 其他参数
    parser.add_argument('--d_model', type=int, default=512, help='模型维度')
    parser.add_argument('--n_heads', type=int, default=8, help='头数')
    parser.add_argument('--e_layers', type=int, default=2, help='编码器层数')
    parser.add_argument('--d_layers', type=int, default=1, help='解码器层数')
    parser.add_argument('--d_ff', type=int, default=2048, help='前馈网络维度')
    parser.add_argument('--moving_avg', type=int, default=25, help='移动平均窗口大小')
    parser.add_argument('--factor', type=int, default=1, help='注意力因子')
    parser.add_argument('--distil', action='store_false', default=True, help='是否在编码器中使用蒸馏')
    parser.add_argument('--dropout', type=float, default=0, help='dropout率')
    parser.add_argument('--embed', type=str, default='timeF', help='时间特征编码')
    parser.add_argument('--activation', type=str, default='gelu', help='激活函数')
    parser.add_argument('--output_attention', action='store_true', help='是否输出编码器中的注意力')
    
    # 特定模型参数
    parser.add_argument('--task_name', type=str, default='long_term_forecast', help='任务名称')
    parser.add_argument('--train_mode', type=int, default=0, help='训练模式')
    parser.add_argument('--base_T', type=int, default=24, help='基本周期')
    parser.add_argument('--H_order', type=int, default=6, help='傅里叶变换阶数')
    parser.add_argument('--SCI', type=int, default=0, help='SCI参数')
    parser.add_argument('--hidden_size', type=int, default=128, help='隐藏层大小')
    
    # GPU设置
    parser.add_argument('--use_gpu', type=int, default=1, help='使用gpu: 1表示是, 0表示否')
    parser.add_argument('--gpu', type=int, default=0, help='GPU设备ID')
    parser.add_argument('--use_multi_gpu', action='store_true', help='使用多个GPU', default=False)
    parser.add_argument('--devices', type=str, default='0', help='多个GPU的设备ID')
    
    # 可视化参数
    parser.add_argument('--plot_result', action='store_true', default=True, help='绘制预测结果与真实值对比图')
    parser.add_argument('--save_prediction', action='store_true', default=True, help='保存预测结果')
    parser.add_argument('--result_file', type=str, default='test_results.txt', help='保存结果的文件名')
    
    # 其他需要传递给模型的参数
    parser.add_argument('--des', type=str, default='test', help='实验描述')
    parser.add_argument('--loss', type=str, default='mse', help='损失函数')
    parser.add_argument('--lradj', type=str, default='type1', help='学习率调整方式')
    parser.add_argument('--train_epochs', type=int, default=10, help='训练周期数')
    parser.add_argument('--itr', type=int, default=1, help='实验次数')
    parser.add_argument('--patience', type=int, default=3, help='早停耐心值')
    parser.add_argument('--learning_rate', type=float, default=0.0001, help='学习率')
    
    args = parser.parse_args()
    
    # GPU设置
    args.use_gpu = True if torch.cuda.is_available() and args.use_gpu == 1 else False
    if args.use_gpu and args.use_multi_gpu:
        args.devices = args.devices.replace(' ', '')
        device_ids = args.devices.split(',')
        args.device_ids = [int(id_) for id_ in device_ids]
        args.gpu = args.device_ids[0]
    elif args.use_gpu:
        args.device_ids = [args.gpu]
    
    # 运行测试
    test_model(args) 