import argparse
import os
import torch
from exp.exp_main import Exp_Main
from exp.exp_CARD_long_term_forecasting import Exp_Long_Term_Forecast as Exp_CARD_Long_Term_Forecast
import random
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd

# 设置随机种子以确保结果可重复
fix_seed = 2021
random.seed(fix_seed)
torch.manual_seed(fix_seed)
np.random.seed(fix_seed)

parser = argparse.ArgumentParser(description='Time Series Forecasting Model Testing')

# 基本配置
parser.add_argument('--model_id', type=str, required=True, default='test', help='model id')
parser.add_argument('--model', type=str, required=True, default='xPatch', help='model name')
parser.add_argument('--checkpoints', type=str, default='./checkpoints/', help='location of model checkpoints')
parser.add_argument('--checkpoint_name', type=str, default='best_model.pth', help='specific checkpoint file name to load (default: best_model.pth)')

# 数据加载器
parser.add_argument('--data', type=str, required=True, default='ETTh1', help='dataset type')
parser.add_argument('--root_path', type=str, default='./dataset', help='root path of the data file')
parser.add_argument('--data_path', type=str, default='ETTh1.csv', help='data file')
parser.add_argument('--features', type=str, default='M', help='forecasting task options:[M, S, MS]')
parser.add_argument('--target', type=str, default='OT', help='target feature in S or MS task')
parser.add_argument('--freq', type=str, default='h', help='freq for time features encoding')

# 预测任务
parser.add_argument('--seq_len', type=int, default=96, help='input sequence length')
parser.add_argument('--label_len', type=int, default=48, help='start token length')
parser.add_argument('--pred_len', type=int, default=96, help='prediction sequence length')

# Patching
parser.add_argument('--patch_len', type=int, default=16, help='patch length')
parser.add_argument('--stride', type=int, default=8, help='stride')
parser.add_argument('--padding_patch', default='end', help='None: None; end: padding on the end')

# Moving Average
parser.add_argument('--ma_type', type=str, default='ema', help='reg, ema, dema')
parser.add_argument('--alpha', type=float, default=0.3, help='alpha')
parser.add_argument('--beta', type=float, default=0.3, help='beta')

# 模型特定参数
parser.add_argument('--FreMLP_embed_size', type=int, default=64, help='FreMLP_embed_size')
parser.add_argument('--FreMLP_hidden_size', type=int, default=256, help='FreMLP_hidden_size')
parser.add_argument('--individual', action='store_true', default=False, help='DLinear: individual')
parser.add_argument('--cycle', type=int, default=24, help='cycle length')
parser.add_argument('--model_type', type=str, default='mlp', help='model type, options: [linear, mlp]')
parser.add_argument('--use_revin', type=int, default=1, help='1: use revin or 0: no revin')
parser.add_argument('--embed_size', default=128, type=int)
parser.add_argument('--period_len', type=int, default=24, help='period_len')
parser.add_argument('--task_name', type=str, default='long_term_forecast', help='task name')

# 优化器和其他参数
parser.add_argument('--enc_in', type=int, default=7, help='encoder input size')
parser.add_argument('--dec_in', type=int, default=7, help='decoder input size')
parser.add_argument('--c_out', type=int, default=7, help='output size')
parser.add_argument('--d_model', type=int, default=512, help='dimension of model')
parser.add_argument('--n_heads', type=int, default=8, help='num of heads')
parser.add_argument('--e_layers', type=int, default=2, help='num of encoder layers')
parser.add_argument('--d_layers', type=int, default=1, help='num of decoder layers')
parser.add_argument('--d_ff', type=int, default=2048, help='dimension of fcn')
parser.add_argument('--moving_avg', type=int, default=25, help='window size of moving average')
parser.add_argument('--factor', type=int, default=1, help='attn factor')
parser.add_argument('--distil', action='store_false', default=True, help='whether to use distilling in encoder')
parser.add_argument('--dropout', type=float, default=0, help='dropout')
parser.add_argument('--embed', type=str, default='timeF', help='time features encoding')
parser.add_argument('--activation', type=str, default='gelu', help='activation')
parser.add_argument('--output_attention', action='store_true', help='whether to output attention in ecoder')

# GPU设置
parser.add_argument('--use_gpu', type=int, default=1, help='use gpu: 1 for True, 0 for False')
parser.add_argument('--gpu', type=int, default=0, help='gpu')
parser.add_argument('--use_multi_gpu', action='store_true', help='use multiple gpus', default=False)
parser.add_argument('--devices', type=str, default='0', help='device ids of multile gpus')

# 添加batch_size参数
parser.add_argument('--batch_size', type=int, default=32, help='batch size for test data loading')
parser.add_argument('--patience', type=int, default=10, help='early stopping patience')
parser.add_argument('--learning_rate', type=float, default=0.0001, help='optimizer learning rate')
parser.add_argument('--des', type=str, default='test', help='exp description')
parser.add_argument('--loss', type=str, default='mse', help='loss function')
parser.add_argument('--lradj', type=str, default='type1', help='adjust learning rate')
parser.add_argument('--train_epochs', type=int, default=10, help='train epochs')
parser.add_argument('--itr', type=int, default=1, help='experiments times')
parser.add_argument('--num_workers', type=int, default=0, help='data loader num workers')

# FITS和CARD特定参数
parser.add_argument('--train_mode', type=int, default=0)
parser.add_argument('--base_T', type=int, default=24)
parser.add_argument('--H_order', type=int, default=6)
parser.add_argument('--SCI', type=int, default=0)
parser.add_argument('--hidden_size', type=int, default=128)

# 可视化参数
parser.add_argument('--plot_result', action='store_true', default=True, help='plot prediction vs ground truth')
parser.add_argument('--num_plots', type=int, default=5, help='number of samples to plot')
parser.add_argument('--save_prediction', action='store_true', default=True, help='save prediction results to npy')
parser.add_argument('--plot_save_dir', type=str, default='./test_plots/', help='directory to save plots')
parser.add_argument('--result_file', type=str, default='test_results.txt', help='file to save test results')

args = parser.parse_args()

# Add compatibility for revin parameter
args.revin = args.use_revin

# 设置其他依赖参数
if hasattr(args, 'cut_freq') and args.cut_freq == 0 and hasattr(args, 'base_T') and hasattr(args, 'H_order'):
    args.cut_freq = int(args.seq_len // args.base_T + 1) * args.H_order + 10

# GPU设置
args.use_gpu = True if torch.cuda.is_available() and args.use_gpu == 1 else False
if args.use_gpu and args.use_multi_gpu:
    args.devices = args.devices.replace(' ', '')
    device_ids = args.devices.split(',')
    args.device_ids = [int(id_) for id_ in device_ids]
    args.gpu = args.device_ids[0]
elif args.use_gpu:
    args.device_ids = [args.gpu]

# 输出参数
print('Test Arguments:')
print(args)

# 选择适当的实验类
if args.model == 'CARD':
    Exp = Exp_CARD_Long_Term_Forecast
else:
    Exp = Exp_Main

# 设置输出目录
setting = '{}_{}_{}_ft{}_sl{}_ll{}_pl{}_{}'.format(
    args.model_id,
    args.model,
    args.data,
    args.features,
    args.seq_len,
    args.label_len,
    args.pred_len,
    'test')

# 创建存储结果和图表的目录
if args.plot_result:
    plot_dir = os.path.join(args.plot_save_dir, setting)
    if not os.path.exists(plot_dir):
        os.makedirs(plot_dir)

# 创建实验实例
exp = Exp(args)

# 尝试预加载数据集但处理可能的异常
try:
    print("Attempting to preload all datasets...")
    from data_provider.data_factory import preload_all_data
    preload_all_data(args)
except Exception as e:
    print(f"Warning: Could not preload datasets: {str(e)}")
    print("Will load datasets on demand instead")

# 设置处于测试模式
args.is_training = 0

# 加载模型并测试
print(f'>>>>>>>Testing : {setting}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<')
exp.test(setting, test=1)

# 如果需要，生成预测
if args.save_prediction:
    print(f'>>>>>>>Predicting : {setting}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<')
    exp.predict(setting, load=True)

print(f'Testing completed. Results saved to {args.result_file}.') 